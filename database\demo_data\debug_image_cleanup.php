<?php
/**
 * Debug Image Cleanup
 * 
 * This script helps debug why images aren't being cleaned up properly.
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';

echo "<h1>Debug Image Cleanup</h1>";
echo "<p>Debugging why images aren't being cleaned up...</p>";

try {
    $db = new Database();
    
    echo "<h2>1. Check Images Table Structure</h2>";
    
    // Check if images table exists
    $db->query("SHOW TABLES LIKE 'images'");
    $imagesTableExists = $db->rowCount() > 0;

    if (!$imagesTableExists) {
        echo "<div style='color: red;'>❌ Images table does not exist!</div>";

        // Check for alternative table names
        echo "<h3>Checking for alternative image table names...</h3>";
        $db->query("SHOW TABLES");
        $allTables = $db->resultSet();

        $imageTables = [];
        foreach ($allTables as $table) {
            $tableName = array_values((array)$table)[0];
            if (strpos(strtolower($tableName), 'image') !== false) {
                $imageTables[] = $tableName;
            }
        }

        if (!empty($imageTables)) {
            echo "<p><strong>Found image-related tables:</strong></p>";
            echo "<ul>";
            foreach ($imageTables as $table) {
                echo "<li>{$table}</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>No image-related tables found.</p>";
        }

        echo "<p>Please check your database structure or create the images table.</p>";
        exit;
    }
    
    // Show table structure
    $db->query("DESCRIBE images");
    $columns = $db->resultSet();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column->Field}</td>";
        echo "<td>{$column->Type}</td>";
        echo "<td>{$column->Null}</td>";
        echo "<td>{$column->Key}</td>";
        echo "<td>{$column->Default}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>2. Check All Images in Database</h2>";
    
    // Get all images
    $db->query("SELECT COUNT(*) as count FROM images");
    $totalResult = $db->single();
    $totalImages = $totalResult->count ?? 0;
    
    echo "<p><strong>Total Images:</strong> {$totalImages}</p>";
    
    if ($totalImages > 0) {
        // Show sample images
        $db->query("SELECT id, file_name, entity_type, entity_id, user_id, created_at FROM images ORDER BY created_at DESC LIMIT 10");
        $sampleImages = $db->resultSet();
        
        echo "<h3>Sample Images (Latest 10)</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Filename</th><th>Entity Type</th><th>Entity ID</th><th>User ID</th><th>Created</th></tr>";
        
        foreach ($sampleImages as $image) {
            echo "<tr>";
            echo "<td>{$image->id}</td>";
            echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
            echo "<td>" . htmlspecialchars($image->entity_type ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($image->entity_id ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($image->user_id ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($image->created_at) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Check Demo Vehicles</h2>";
    
    // Check demo vehicles
    $db->query("SELECT id, vin, license_plate FROM vehicles WHERE vin REGEXP '^DEMO[0-9]{13}$' AND license_plate REGEXP '^DEMO[0-9]{3}$'");
    $demoVehicles = $db->resultSet();
    
    echo "<p><strong>Demo Vehicles Found:</strong> " . count($demoVehicles) . "</p>";
    
    if (!empty($demoVehicles)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>VIN</th><th>License Plate</th></tr>";
        foreach ($demoVehicles as $vehicle) {
            echo "<tr>";
            echo "<td>{$vehicle->id}</td>";
            echo "<td>{$vehicle->vin}</td>";
            echo "<td>{$vehicle->license_plate}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check images for these vehicles
        $demoVehicleIds = array_column($demoVehicles, 'id');
        $placeholders = str_repeat('?,', count($demoVehicleIds) - 1) . '?';
        
        $stmt = $db->getConnection()->prepare("SELECT * FROM images WHERE entity_type = 'vehicle' AND entity_id IN ({$placeholders})");
        $stmt->execute($demoVehicleIds);
        $vehicleImages = $stmt->fetchAll(PDO::FETCH_OBJ);
        
        echo "<p><strong>Images for Demo Vehicles:</strong> " . count($vehicleImages) . "</p>";
        
        if (!empty($vehicleImages)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Image ID</th><th>Filename</th><th>Vehicle ID</th><th>File Path</th></tr>";
            foreach ($vehicleImages as $image) {
                echo "<tr>";
                echo "<td>{$image->id}</td>";
                echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
                echo "<td>{$image->entity_id}</td>";
                echo "<td>" . htmlspecialchars($image->file_path ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h2>4. Check Demo Shows</h2>";
    
    // Check demo shows
    $demoShowNames = [
        'Southern California Classic Car Showcase',
        'Miami Beach Exotic Car Festival',
        'Texas Muscle Car Madness',
        'Arizona Desert Classic Concours',
        'Atlanta Import Tuner Expo',
        'Rocky Mountain Vintage Rally',
        'Pacific Northwest Euro Fest',
        'Music City Hot Rod Nationals',
        'Charlotte Motor Speedway Car Show',
        'Las Vegas Strip Supercar Spectacular'
    ];
    
    $demoShows = [];
    foreach ($demoShowNames as $showName) {
        $stmt = $db->getConnection()->prepare("SELECT id, name FROM shows WHERE name = ?");
        $stmt->execute([$showName]);
        $show = $stmt->fetch(PDO::FETCH_OBJ);
        if ($show) {
            $demoShows[] = $show;
        }
    }
    
    echo "<p><strong>Demo Shows Found:</strong> " . count($demoShows) . "</p>";
    
    if (!empty($demoShows)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th></tr>";
        foreach ($demoShows as $show) {
            echo "<tr>";
            echo "<td>{$show->id}</td>";
            echo "<td>" . htmlspecialchars($show->name) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check images for these shows
        $demoShowIds = array_column($demoShows, 'id');
        $placeholders = str_repeat('?,', count($demoShowIds) - 1) . '?';
        
        $stmt = $db->getConnection()->prepare("SELECT * FROM images WHERE entity_type = 'show' AND entity_id IN ({$placeholders})");
        $stmt->execute($demoShowIds);
        $showImages = $stmt->fetchAll(PDO::FETCH_OBJ);
        
        echo "<p><strong>Images for Demo Shows:</strong> " . count($showImages) . "</p>";
        
        if (!empty($showImages)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Image ID</th><th>Filename</th><th>Show ID</th><th>File Path</th></tr>";
            foreach ($showImages as $image) {
                echo "<tr>";
                echo "<td>{$image->id}</td>";
                echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
                echo "<td>{$image->entity_id}</td>";
                echo "<td>" . htmlspecialchars($image->file_path ?? 'NULL') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    echo "<h2>5. Check Demo Pattern Images</h2>";
    
    // Check demo pattern images
    $patterns = ['demo_vehicle_%', 'demo_show_%', 'demo_car_%', 'demo_event_%'];
    $patternImages = [];
    
    foreach ($patterns as $pattern) {
        $stmt = $db->getConnection()->prepare("SELECT * FROM images WHERE file_name LIKE ?");
        $stmt->execute([$pattern]);
        $images = $stmt->fetchAll(PDO::FETCH_OBJ);
        $patternImages = array_merge($patternImages, $images);
    }
    
    echo "<p><strong>Demo Pattern Images Found:</strong> " . count($patternImages) . "</p>";
    
    if (!empty($patternImages)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Image ID</th><th>Filename</th><th>Entity Type</th><th>Entity ID</th></tr>";
        foreach ($patternImages as $image) {
            echo "<tr>";
            echo "<td>{$image->id}</td>";
            echo "<td>" . htmlspecialchars($image->file_name) . "</td>";
            echo "<td>" . htmlspecialchars($image->entity_type ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($image->entity_id ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>6. Check ImageEditorModel</h2>";
    
    // Check if ImageEditorModel exists
    $modelPath = '../../models/ImageEditorModel.php';
    if (file_exists($modelPath)) {
        echo "<p style='color: green;'>✅ ImageEditorModel found at: {$modelPath}</p>";
        
        try {
            require_once $modelPath;
            $imageEditorModel = new ImageEditorModel();
            echo "<p style='color: green;'>✅ ImageEditorModel loaded successfully</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error loading ImageEditorModel: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ ImageEditorModel not found at: {$modelPath}</p>";
    }
    
    echo "<h2>Summary</h2>";
    echo "<ul>";
    echo "<li>Total Images: {$totalImages}</li>";
    echo "<li>Demo Vehicles: " . count($demoVehicles) . "</li>";
    echo "<li>Demo Shows: " . count($demoShows) . "</li>";
    echo "<li>Demo Pattern Images: " . count($patternImages) . "</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 15px 0;'>";
    echo "<strong>❌ Error:</strong> " . $e->getMessage();
    echo "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='index.php'>← Back to Demo Data Generator</a></p>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

table {
    margin: 15px 0;
    font-size: 12px;
    width: 100%;
}

th, td {
    padding: 6px 10px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f8f9fa;
    font-weight: bold;
}
</style>
