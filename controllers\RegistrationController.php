<?php
/**
 * Registration Controller
 * 
 * This controller handles all registration-related functionality.
 */
class RegistrationController extends Controller {
    private $showModel;
    private $vehicleModel;
    private $registrationModel;
    private $paymentModel;
    private $auth;
    private $entityTemplateManager;
    private $defaultTemplateManager;
    private $formDesignerModel;
    private $printableTemplateModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->showModel = $this->model('ShowModel');
        $this->vehicleModel = $this->model('VehicleModel');
        $this->registrationModel = $this->model('RegistrationModel');
        $this->paymentModel = $this->model('PaymentModel');
        $this->auth = new Auth();
        
        // Load template managers
        require_once APPROOT . '/models/EntityTemplateManager.php';
        require_once APPROOT . '/models/DefaultTemplateManager.php';
        $this->entityTemplateManager = new EntityTemplateManager();
        $this->defaultTemplateManager = new DefaultTemplateManager();
        $this->formDesignerModel = $this->model('FormDesignerModel');
        $this->printableTemplateModel = $this->model('PrintableTemplateModel');
        
        // Ensure user is logged in for all registration actions
        if (!$this->auth->isLoggedIn() && $_SERVER['REQUEST_METHOD'] != 'GET') {
            $this->redirect('auth/login');
        }
    }
    
    /**
     * Print a registration using a printable template
     * 
     * @param int $registrationId Registration ID
     * @param int|null $templateId Template ID (optional, uses default if not provided)
     */
    public function printRegistration($registrationId, $templateId = null) {
        // Get registration details
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has access to this registration
        $isAdmin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
        $isCoordinator = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'coordinator';
        $isOwner = $this->auth->isLoggedIn() && $_SESSION['user_id'] == $registration->owner_id;
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is the coordinator for this specific show
        $isShowCoordinator = false;
        if ($isCoordinator && isset($show->coordinator_id) && $show->coordinator_id == $_SESSION['user_id']) {
            $isShowCoordinator = true;
        }
        
        // Only allow access if user is admin, show coordinator, or the registration owner
        if (!$isAdmin && !$isShowCoordinator && !$isOwner) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Check if QR code has been generated
        if (!isset($registration->qr_code) || empty($registration->qr_code)) {
            // If admin or coordinator, redirect to QR code generation
            if ($isAdmin || $isShowCoordinator) {
                // Redirect to QR code generation
                $this->redirect('admin/generateQrCode/' . $registrationId . '?return=' . urlencode('registration/printRegistration/' . $registrationId));
                return;
            } else {
                // For regular users, show an error message
                $this->view('registration/no_qr_code', [
                    'registration' => $registration,
                    'show' => $show
                ]);
                return;
            }
        }
        
        // Get vehicle details
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
        
        // Get category details
        $category = $this->showModel->getCategoryById($registration->category_id);
        
        // Get user details
        $user = $this->model('UserModel')->getUserById($registration->owner_id);
        
        // Create a direct URL to the QR code file
        $qrCodeUrl = '';
        if (!empty($registration->qr_code)) {
            // Try direct link to the file
            $qrCodeUrl = '/uploads/qrcodes/' . $registration->qr_code;
            error_log("RegistrationController::printRegistration - Created direct QR code URL: " . $qrCodeUrl);
        } else {
            $qrCodeUrl = '/public/assets/images/no-qr-code.png'; // Fallback image
            error_log("RegistrationController::printRegistration - Using fallback QR code URL: " . $qrCodeUrl);
        }
        
        // Prepare data for template with proper null checks
        $data = [
            // Registration data
            'registration_number' => $registration->registration_number ?? $registration->id,
            'registration_date' => !empty($registration->created_at) ? convertUTCToUserDateTime($registration->created_at, $_SESSION['user_id'], 'F j, Y') : 'N/A',
            'registration_id' => $registration->id,
            'display_number' => $registration->display_number ?? ($registration->registration_number ?? $registration->id),
            'registration_status' => $registration->status ?? 'N/A',
            'payment_status' => $registration->payment_status ?? 'N/A',
            'payment_method' => $registration->payment_method ?? 'N/A',
            'payment_amount' => $registration->payment_amount ?? 'N/A',
            'checked_in' => $registration->checked_in ? 'Yes' : 'No',
            
            // Show data
            'show_name' => $show->name ?? 'N/A',
            'show_date' => !empty($show->event_date) ? formatDateTimeForUser($show->event_date, $this->auth->getCurrentUserId(), 'F j, Y') : 
                          (!empty($show->start_date) ? formatDateTimeForUser($show->start_date, $this->auth->getCurrentUserId(), 'F j, Y') : 'N/A'),
            'show_location' => $show->location ?? 'N/A',
            'show_id' => $show->id ?? 'N/A',
            'show_description' => $show->description ?? 'N/A',
            
            // Vehicle data
            'vehicle_year' => $vehicle->year ?? 'N/A',
            'vehicle_make' => $vehicle->make ?? 'N/A',
            'vehicle_model' => $vehicle->model ?? 'N/A',
            'vehicle_color' => $vehicle->color ?? 'N/A',
            'vehicle_license' => $vehicle->license_plate ?? 'N/A',
            'vehicle_id' => $vehicle->id ?? 'N/A',
            'vehicle_vin' => $vehicle->vin ?? 'N/A',
            'year' => $vehicle->year ?? 'N/A',
            'make' => $vehicle->make ?? 'N/A',
            'model' => $vehicle->model ?? 'N/A',
            'color' => $vehicle->color ?? 'N/A',
            'license_plate' => $vehicle->license_plate ?? 'N/A',
            
            // Category data
            'category_name' => $category->name ?? 'N/A',
            'category_id' => $category->id ?? 'N/A',
            'category' => $category->name ?? 'N/A',
            
            // Owner data
            'owner_name' => $user->name ?? 'N/A',
            'owner_email' => $user->email ?? 'N/A',
            'owner_phone' => $user->phone ?? 'N/A',
            'owner_id' => $user->id ?? 'N/A',
            'name' => $user->name ?? 'N/A',
            'email' => $user->email ?? 'N/A',
            'phone' => $user->phone ?? 'N/A',
            
            // QR code - store both the HTML and the raw filename
            'qr_code' => '<img src="' . $qrCodeUrl . '" alt="QR Code" style="width:100%; height:auto; max-width:300px; display:block; margin:0 auto;">' . 
                (!empty($registration->qr_code) ? '' : '<div class="alert alert-warning">QR code has not been generated yet. Please contact the event coordinator.</div>'),
            'qrcode' => '<img src="' . $qrCodeUrl . '" alt="QR Code" style="width:100%; height:auto; max-width:300px; display:block; margin:0 auto;">' . 
                (!empty($registration->qr_code) ? '' : '<div class="alert alert-warning">QR code has not been generated yet. Please contact the event coordinator.</div>'),
            'qr_code_file' => !empty($registration->qr_code) ? $registration->qr_code : '',
            'qrcode_file' => !empty($registration->qr_code) ? $registration->qr_code : '',
            'registration_id' => $registration->id,
        ];
        
        // Check if CustomFieldValuesModel has the required method
        $customFieldValuesModel = $this->model('CustomFieldValuesModel');
        if (method_exists($customFieldValuesModel, 'getCustomFieldValues')) {
            // Try to get custom field values for the show
            $customFieldValues = $customFieldValuesModel->getCustomFieldValues($show->id);
            if ($customFieldValues && is_array($customFieldValues)) {
                foreach ($customFieldValues as $fieldId => $fieldData) {
                    // Check if the data is in the expected format
                    if (is_array($fieldData) && isset($fieldData['value'])) {
                        $data['custom_' . $fieldId] = $fieldData['value'];
                    }
                }
            }
        }
        
        // Debug data
        error_log("RegistrationController::printRegistration - Data prepared for template: " . print_r($data, true));
        
        // Debug QR code information
        error_log("RegistrationController::printRegistration - QR code filename: " . ($registration->qr_code ?? 'Not available'));
        error_log("RegistrationController::printRegistration - QR code path: /uploads/qrcodes/" . ($registration->qr_code ?? ''));
        
        // Check if the QR code file exists
        $qrCodePath = $_SERVER['DOCUMENT_ROOT'] . '/uploads/qrcodes/' . ($registration->qr_code ?? '');
        error_log("RegistrationController::printRegistration - Full QR code path: " . $qrCodePath);
        error_log("RegistrationController::printRegistration - QR code file exists: " . (file_exists($qrCodePath) ? 'Yes' : 'No'));
        
        // Get the default template from the database
        $defaultTemplate = $this->printableTemplateModel->getDefaultTemplate();
        error_log("RegistrationController::printRegistration - Default template: " . ($defaultTemplate ? "Found (ID: {$defaultTemplate->id})" : "Not found"));
        
        // Use the specified template ID if provided, otherwise use the default template
        $templateToUse = $templateId ? $templateId : ($defaultTemplate ? $defaultTemplate->id : null);
        error_log("RegistrationController::printRegistration - Using template ID: " . ($templateToUse ?? "None"));
        
        // Render the template
        $html = $this->printableTemplateModel->renderTemplate($templateToUse, $data);
        
        if ($html === false || empty($html)) {
            // If template rendering failed, use the fallback view
            error_log("RegistrationController::printRegistration - Template rendering failed, using fallback view");
            $this->view('registration/print', $data);
        } else {
            // Add debug information at the top of the page (only visible in source)
            $debugInfo = "<!-- 
DEBUG INFO:
Registration ID: {$registration->id}
QR Code: " . (empty($registration->qr_code) ? 'Not available' : $registration->qr_code) . "
Template ID: " . ($templateToUse ?? 'None') . "
Data Keys: " . implode(', ', array_keys($data)) . "
-->";
            
            // Output the HTML directly with debug info
            echo $debugInfo . $html;
        }
        exit;
    }
    
    /**
     * View a specific registration
     * 
     * @param int $id Registration ID
     */
    public function viewRegistration($id) {
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has access to this registration
        $isAdmin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
        $isCoordinator = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'coordinator';
        $isOwner = $this->auth->isLoggedIn() && $_SESSION['user_id'] == $registration->owner_id;
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is the coordinator for this specific show
        $isShowCoordinator = false;
        if ($isCoordinator && isset($show->coordinator_id) && $show->coordinator_id == $_SESSION['user_id']) {
            $isShowCoordinator = true;
        }
        
        // Only allow access if user is admin, show coordinator, or the registration owner
        if (!$isAdmin && !$isShowCoordinator && !$isOwner) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get vehicle details
        $vehicle = $this->vehicleModel->getVehicleById($registration->vehicle_id);
        
        // Get category details
        $category = $this->showModel->getCategoryById($registration->category_id);
        
        // Get user details
        $user = $this->model('UserModel')->getUserById($registration->owner_id);
        
        // Get vehicle images
        $imageEditorModel = $this->model('ImageEditorModel');
        $allImages = $imageEditorModel->getImagesByEntity('vehicle', $registration->vehicle_id);
        
        // Find primary image
        $primaryImage = null;
        foreach ($allImages as $image) {
            if (isset($image->is_primary) && $image->is_primary) {
                $primaryImage = $image;
                break;
            }
        }
        
        // If no primary image is set, use the first image
        if (!$primaryImage && count($allImages) > 0) {
            $primaryImage = $allImages[0];
            $primaryImage->is_primary = 1;
        }
        
        // Get judging results if show is completed
        $judgingResults = [];
        $awards = [];
        
        if ($show && $show->status === 'completed') {
            $judgingModel = $this->model('JudgingModel');
            $judgingResults = $judgingModel->getJudgingResultsByRegistration($id);
            $awards = $judgingModel->getAwardsByRegistration($id);
        }
        
        $data = [
            'title' => 'Registration Details',
            'registration' => $registration,
            'show' => $show,
            'vehicle' => $vehicle,
            'category' => $category,
            'user' => $user,
            'vehicle_images' => $allImages,
            'primary_image' => $primaryImage,
            'judging_results' => $judgingResults,
            'awards' => $awards
        ];
        
        $this->view('registration/view', $data);
    }
    
    /**
     * Public view method for URL routing compatibility
     * This method handles both view template loading and registration viewing
     * 
     * @param mixed $viewOrId Either a view path string or registration ID
     * @param array $data Optional data array for view templates
     * @return void
     */
    public function view($viewOrId, $data = []) {
        // If this is a call to the parent view method (with a view name), pass it through
        if (is_string($viewOrId) && strpos($viewOrId, '/') !== false) {
            parent::view($viewOrId, $data);
            return;
        }
        
        // Otherwise, treat it as a registration ID and call viewRegistration
        $this->viewRegistration($viewOrId);
    }
    
    /**
     * Display QR code directly
     * 
     * @param int $registrationId Registration ID
     */
    public function displayQrCode($registrationId) {
        error_log("RegistrationController::displayQrCode - Called with ID: " . $registrationId);
        
        // Get registration details
        $registration = $this->registrationModel->getRegistrationById($registrationId);
        
        if (!$registration) {
            error_log("RegistrationController::displayQrCode - Registration not found for ID: " . $registrationId);
            header('HTTP/1.0 404 Not Found');
            echo 'Registration not found';
            exit;
        }
        
        error_log("RegistrationController::displayQrCode - Registration found: " . print_r($registration, true));
        
        if (empty($registration->qr_code)) {
            error_log("RegistrationController::displayQrCode - QR code is empty for registration ID: " . $registrationId);
            header('HTTP/1.0 404 Not Found');
            echo 'QR code not found';
            exit;
        }
        
        // Check if user has access to this registration
        $isAdmin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
        $isCoordinator = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'coordinator';
        $isOwner = $this->auth->isLoggedIn() && $_SESSION['user_id'] == $registration->owner_id;
        
        // Get show details
        $show = $this->showModel->getShowById($registration->show_id);
        
        // Check if user is the coordinator for this specific show
        $isShowCoordinator = false;
        if ($isCoordinator && isset($show->coordinator_id) && $show->coordinator_id == $_SESSION['user_id']) {
            $isShowCoordinator = true;
        }
        
        // Only allow access if user is admin, show coordinator, or the registration owner
        if (!$isAdmin && !$isShowCoordinator && !$isOwner) {
            header('HTTP/1.0 403 Forbidden');
            echo 'Access denied';
            exit;
        }
        
        // Try multiple paths to find the QR code file
        $possiblePaths = [
            $_SERVER['DOCUMENT_ROOT'] . '/uploads/qrcodes/' . $registration->qr_code,
            APPROOT . '/uploads/qrcodes/' . $registration->qr_code,
            dirname(APPROOT) . '/uploads/qrcodes/' . $registration->qr_code,
            $_SERVER['DOCUMENT_ROOT'] . '/public/uploads/qrcodes/' . $registration->qr_code,
            APPROOT . '/public/uploads/qrcodes/' . $registration->qr_code,
            dirname(APPROOT) . '/public/uploads/qrcodes/' . $registration->qr_code,
            // Add more paths if needed
        ];
        
        $qrCodePath = null;
        foreach ($possiblePaths as $path) {
            error_log("RegistrationController::displayQrCode - Trying path: " . $path);
            if (file_exists($path)) {
                $qrCodePath = $path;
                error_log("RegistrationController::displayQrCode - Found QR code at: " . $qrCodePath);
                break;
            }
        }
        
        if (!$qrCodePath) {
            error_log("RegistrationController::displayQrCode - QR code file not found in any location");
            
            // Check if directories exist
            $dirs = [
                $_SERVER['DOCUMENT_ROOT'] . '/uploads/qrcodes/',
                APPROOT . '/uploads/qrcodes/',
                dirname(APPROOT) . '/uploads/qrcodes/'
            ];
            
            foreach ($dirs as $dir) {
                error_log("RegistrationController::displayQrCode - Directory " . $dir . " exists: " . (is_dir($dir) ? 'Yes' : 'No'));
            }
            
            // Try to serve a default QR code image
            $defaultQrPath = APPROOT . '/public/assets/images/no-qr-code.png';
            if (file_exists($defaultQrPath)) {
                error_log("RegistrationController::displayQrCode - Using default QR code image");
                $qrCodePath = $defaultQrPath;
            } else {
                header('HTTP/1.0 404 Not Found');
                echo 'QR code file not found';
                exit;
            }
        }
        
        // Get the file extension
        $extension = pathinfo($qrCodePath, PATHINFO_EXTENSION);
        $contentType = 'image/png'; // Default
        
        // Set the appropriate content type
        if ($extension === 'jpg' || $extension === 'jpeg') {
            $contentType = 'image/jpeg';
        } elseif ($extension === 'gif') {
            $contentType = 'image/gif';
        }
        
        // Output the image
        header('Content-Type: ' . $contentType);
        header('Content-Length: ' . filesize($qrCodePath));
        readfile($qrCodePath);
        exit;
    }
    
    /**
     * Render a fallback template if no printable template is available
     * 
     * @param array $data Template data
     * @return string HTML
     */
    private function renderFallbackTemplate($data) {
        ob_start();
        ?><!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registration - <?php echo htmlspecialchars($data['show_name']); ?></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .registration-details {
            margin-bottom: 20px;
        }
        .vehicle-details {
            margin-bottom: 20px;
        }
        .owner-details {
            margin-bottom: 20px;
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 0.8em;
            color: #777;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table td {
            padding: 8px;
        }
        table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        @media print {
            body {
                background-color: white;
            }
            .container {
                box-shadow: none;
                max-width: 100%;
            }
            .no-print {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo htmlspecialchars($data['show_name']); ?></h1>
            <h2>Registration Confirmation</h2>
        </div>
        
        <div class="registration-details">
            <h3>Registration Information</h3>
            <table>
                <tr>
                    <td><strong>Registration #:</strong></td>
                    <td><?php echo $data['registration_number']; ?></td>
                </tr>
                <tr>
                    <td><strong>Registration Date:</strong></td>
                    <td><?php echo $data['registration_date']; ?></td>
                </tr>
                <tr>
                    <td><strong>Show Date:</strong></td>
                    <td><?php echo $data['show_date']; ?></td>
                </tr>
                <tr>
                    <td><strong>Show Location:</strong></td>
                    <td><?php echo htmlspecialchars($data['show_location']); ?></td>
                </tr>
                <?php if (!empty($data['category_name']) && $data['category_name'] !== 'N/A'): ?>
                <tr>
                    <td><strong>Category:</strong></td>
                    <td><?php echo htmlspecialchars($data['category_name']); ?></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        
        <div class="vehicle-details">
            <h3>Vehicle Information</h3>
            <table>
                <tr>
                    <td><strong>Year:</strong></td>
                    <td><?php echo htmlspecialchars($data['vehicle_year']); ?></td>
                </tr>
                <tr>
                    <td><strong>Make:</strong></td>
                    <td><?php echo htmlspecialchars($data['vehicle_make']); ?></td>
                </tr>
                <tr>
                    <td><strong>Model:</strong></td>
                    <td><?php echo htmlspecialchars($data['vehicle_model']); ?></td>
                </tr>
                <?php if (!empty($data['vehicle_color']) && $data['vehicle_color'] !== 'N/A'): ?>
                <tr>
                    <td><strong>Color:</strong></td>
                    <td><?php echo htmlspecialchars($data['vehicle_color']); ?></td>
                </tr>
                <?php endif; ?>
                <?php if (!empty($data['vehicle_license']) && $data['vehicle_license'] !== 'N/A'): ?>
                <tr>
                    <td><strong>License Plate:</strong></td>
                    <td><?php echo htmlspecialchars($data['vehicle_license']); ?></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        
        <div class="owner-details">
            <h3>Owner Information</h3>
            <table>
                <tr>
                    <td><strong>Name:</strong></td>
                    <td><?php echo htmlspecialchars($data['owner_name']); ?></td>
                </tr>
                <tr>
                    <td><strong>Email:</strong></td>
                    <td><?php echo htmlspecialchars($data['owner_email']); ?></td>
                </tr>
                <?php if (!empty($data['owner_phone']) && $data['owner_phone'] !== 'N/A'): ?>
                <tr>
                    <td><strong>Phone:</strong></td>
                    <td><?php echo htmlspecialchars($data['owner_phone']); ?></td>
                </tr>
                <?php endif; ?>
            </table>
        </div>
        
        <div class="qr-code">
            <h3>QR Code</h3>
            <p>Scan this QR code for quick access to your registration information.</p>
            <?php 
            // Debug QR code data
            error_log("RegistrationController::renderFallbackTemplate - QR code data: " . $data['qr_code']);
            
            // Output the QR code
            echo $data['qr_code']; 
            ?>
        </div>
        
        <div class="footer">
            <p>This registration confirmation was generated by the Events and Shows Management System.</p>
            <p>Please bring this confirmation with you to the event.</p>
        </div>
    </div>
    
    <div class="no-print" style="text-align: center; margin: 20px;">
        <button onclick="window.print();" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
            Print Registration
        </button>
        <button onclick="window.close();" style="padding: 10px 20px; background-color: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">
            Close
        </button>
    </div>
</body>
</html><?php
        return ob_get_clean();
    }
    
    /**
     * Registration index
     */
    public function index() {
        // Redirect to user registrations
        $this->redirect('user/registrations');
    }
    
    /**
     * Register for a show
     * 
     * @param int $showId Show ID
     */
    public function register($showId = null) {
        // If no show ID provided, redirect to calendar to select a show
        if (!$showId) {
            $this->setFlashMessage('Please select a show to register for.', 'info');
            $this->redirect('calendar');
            return;
        }
        
        // Ensure user is logged in
        if (!$this->auth->isLoggedIn()) {
            // Store the intended destination for after login
            $_SESSION['redirect_after_login'] = 'registration/register/' . $showId;
            $this->redirect('auth/login');
            return;
        }
        
        // Get show
        $show = $this->showModel->getShowById($showId);
        
        if (!$show || $show->status != 'published') {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user is admin or coordinator
        $isAdmin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
        $isCoordinator = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'coordinator';
        $isShowCoordinator = false;
        
        // Check if user is the coordinator for this specific show
        if ($isCoordinator && isset($show->coordinator_id) && $show->coordinator_id == $_SESSION['user_id']) {
            $isShowCoordinator = true;
        }
        
        // Admin override for registration dates
        $adminOverride = $isAdmin || $isShowCoordinator;
        
        // Check if registration is open (with admin override if applicable)
        $registrationOpen = $this->showModel->isRegistrationOpen($showId, $adminOverride);
        
        if (!$registrationOpen) {
            $this->redirect('home/error/Registration%20is%20currently%20closed%20for%20this%20show');
            return;
        }
        
        // Get user vehicles
        $userId = $_SESSION['user_id'];
        $vehicles = $this->vehicleModel->getUserVehicles($userId);
        
        // Get show categories
        $categories = $this->showModel->getShowCategories($showId);
        error_log("RegistrationController::register - Categories count: " . count($categories));
        
        // Debug the first few categories
        $categoryCount = min(count($categories), 3);
        for ($i = 0; $i < $categoryCount; $i++) {
            error_log("RegistrationController::register - Category {$i}: ID={$categories[$i]->id}, Name={$categories[$i]->name}, Fee={$categories[$i]->registration_fee}");
        }
        
        // Get registration counts for each category
        $registration_counts = $this->registrationModel->countRegistrationsByCategory($showId);
        
        // Add registration counts to show object
        $show->registration_counts = $registration_counts;
        
        // Get payment methods
        $paymentMethods = $this->paymentModel->getPaymentMethods();
        
        // Get template for this show's registrations
        // First check if there's a show-specific template
        $showTemplate = $this->entityTemplateManager->getEntityTemplateId('show', $showId);
        error_log("RegistrationController::register - Show-specific template ID: " . ($showTemplate ?: 'None'));
        
        if ($showTemplate) {
            $template = $this->formDesignerModel->getFormTemplateById($showTemplate);
            $templateSource = 'show-specific';
            error_log("RegistrationController::register - Found show-specific template: " . ($template ? $template->name : 'None'));
        } else {
            // If no show-specific template, check for default registration template
            $defaultTemplate = $this->defaultTemplateManager->getDefaultTemplate('registration');
            error_log("RegistrationController::register - Default registration template: " . ($defaultTemplate ? $defaultTemplate->name : 'None'));
            
            if ($defaultTemplate) {
                $template = $defaultTemplate;
                $templateSource = 'default';
                error_log("RegistrationController::register - Using default template: " . $template->name);
            } else {
                // If no default template, use a system default template
                error_log("RegistrationController::register - No default template found, checking for system template");
                
                // Try to get a system default template for registrations
                $systemTemplate = $this->formDesignerModel->getFormTemplateByTypeAndEntity('show', 0);
                error_log("RegistrationController::register - System template: " . ($systemTemplate ? $systemTemplate->name : 'None'));
                
                if ($systemTemplate) {
                    $template = $systemTemplate;
                    $templateSource = 'system';
                } else {
                    $template = null;
                    $templateSource = 'none';
                }
            }
        }
        
        // If we have a template, check if it has the required fields
        if ($template && isset($template->fields)) {
            $fields = json_decode($template->fields, true);
            $requiredFields = ['vehicle_id', 'category_id', 'payment_method_id', 'year_of_manufacture'];
            $missingFields = [];
            
            if (is_array($fields)) {
                // Check for each required field
                foreach ($requiredFields as $requiredField) {
                    $fieldFound = false;
                    foreach ($fields as $field) {
                        if (isset($field['id']) && $field['id'] === $requiredField) {
                            $fieldFound = true;
                            error_log("RegistrationController::register - Template has {$requiredField} field with options: " . 
                                     (isset($field['options']) ? (is_array($field['options']) ? 'Array' : $field['options']) : 'None'));
                            break;
                        }
                    }
                    
                    if (!$fieldFound) {
                        $missingFields[] = $requiredField;
                    }
                }
            }
            
            if (!empty($missingFields)) {
                error_log("RegistrationController::register - WARNING: Template is missing required fields: " . implode(', ', $missingFields));
                
                // If the template is missing critical fields, we should add them
                if (!is_array($fields)) {
                    $fields = [];
                }
                
                // Add missing fields to the template
                foreach ($missingFields as $missingField) {
                    error_log("RegistrationController::register - Adding missing field to template: {$missingField}");
                    
                    switch ($missingField) {
                        case 'vehicle_id':
                            $fields[] = [
                                'id' => 'vehicle_id',
                                'type' => 'select',
                                'label' => 'Select Vehicle',
                                'required' => true,
                                'options' => 'vehicles',
                                'width' => 'col-12',
                                'placeholder' => 'Choose your vehicle'
                            ];
                            break;
                            
                        case 'category_id':
                            $fields[] = [
                                'id' => 'category_id',
                                'type' => 'select',
                                'label' => 'Vehicle Category',
                                'required' => true,
                                'options' => 'categories',
                                'width' => 'col-12',
                                'placeholder' => 'Select category'
                            ];
                            break;
                            
                        case 'payment_method_id':
                            $fields[] = [
                                'id' => 'payment_method_id',
                                'type' => 'select',
                                'label' => 'Payment Method',
                                'required' => true,
                                'options' => 'payment_methods',
                                'width' => 'col-12',
                                'placeholder' => 'Select payment method'
                            ];
                            break;
                            
                        case 'year_of_manufacture':
                            $fields[] = [
                                'id' => 'year_of_manufacture',
                                'type' => 'select',
                                'label' => 'Year of Manufacture',
                                'required' => true,
                                'width' => 'col-md-6',
                                'placeholder' => 'Select year'
                            ];
                            break;
                    }
                }
                
                // Update the template with the added fields
                $template->fields = json_encode($fields);
                error_log("RegistrationController::register - Updated template with missing fields");
            }
        }
        
        // Log which template we're using
        error_log("RegistrationController::register - Using {$templateSource} template for show ID {$showId}");
        
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Sanitize POST data
            $_POST = $this->sanitizeInput($_POST);
            
            // Get form data
            $vehicleId = isset($_POST['vehicle_id']) ? intval($_POST['vehicle_id']) : 0;
            $categoryId = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
            $paymentMethodId = isset($_POST['payment_method_id']) ? intval($_POST['payment_method_id']) : 0;
            
            // Validate form data
            $errors = [];
            
            if (empty($vehicleId)) {
                $errors['vehicle_id'] = 'Please select a vehicle';
            } else {
                // Verify vehicle belongs to user
                $vehicle = $this->vehicleModel->getVehicleById($vehicleId);
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $errors['vehicle_id'] = 'Invalid vehicle selection';
                }
                
                // Check if vehicle is already registered for this show
                if ($this->registrationModel->isVehicleRegistered($showId, $vehicleId)) {
                    $errors['vehicle_id'] = 'This vehicle is already registered for this show';
                }
            }
            
            if (empty($categoryId)) {
                $errors['category_id'] = 'Please select a category';
            } else {
                // Verify category belongs to show
                $category = $this->showModel->getCategoryById($categoryId);
                if (!$category || $category->show_id != $showId) {
                    $errors['category_id'] = 'Invalid category selection';
                }
                
                // Check if category is full
                if ($category->max_entries > 0) {
                    $categoryCount = 0;
                    foreach ($this->registrationModel->countRegistrationsByCategory($showId) as $count) {
                        if ($count->id == $categoryId) {
                            $categoryCount = $count->count;
                            break;
                        }
                    }
                    
                    if ($categoryCount >= $category->max_entries) {
                        $errors['category_id'] = 'This category is full';
                    }
                }
            }
            
            // Check if show is free
            $isFreeShow = isset($show->is_free) && $show->is_free == 1;
            
            // Only validate payment method for paid shows
            if (!$isFreeShow) {
                if (empty($paymentMethodId)) {
                    $errors['payment_method_id'] = 'Please select a payment method';
                } else {
                    // Verify payment method exists
                    $paymentMethod = $this->paymentModel->getPaymentMethodById($paymentMethodId);
                    if (!$paymentMethod) {
                        $errors['payment_method_id'] = 'Invalid payment method selection';
                    }
                }
            }
            
            // If no errors, create registration
            if (empty($errors)) {
                // Get category fee
                $fee = 0;
                foreach ($categories as $cat) {
                    if ($cat->id == $categoryId) {
                        $fee = $cat->registration_fee;
                        break;
                    }
                }
                
                // Check if show is free
                $isFreeShow = isset($show->is_free) && $show->is_free == 1;
                
                // If show is free, set fee to 0 and payment status to 'free'
                if ($isFreeShow) {
                    $fee = 0;
                    $paymentStatus = 'free';
                    $initialStatus = 'approved'; // Auto-approve free registrations
                } else {
                    $paymentStatus = 'pending';
                    $initialStatus = 'pending';
                }
                
                // Create registration data
                $registrationData = [
                    'show_id' => $showId,
                    'category_id' => $categoryId,
                    'vehicle_id' => $vehicleId,
                    'user_id' => $_SESSION['user_id'], // Add user_id from session
                    'status' => $initialStatus,
                    'fee' => $fee,
                    'payment_method_id' => $isFreeShow ? null : $paymentMethodId, // No payment method needed for free shows
                    'payment_status' => $paymentStatus
                ];
                
                // Create registration
                $registrationId = $this->registrationModel->createRegistration($registrationData);
                
                if ($registrationId) {
                    // Check if show is free or if the fee is 0
                    if ($isFreeShow || $fee <= 0) {
                        // For free shows, we've already set the status to approved and payment_status to free
                        // No need to update anything further
                        
                        // Set a flash message to inform the user about QR code generation
                        $_SESSION['flash_message'] = [
                            'type' => 'success',
                            'message' => 'Registration successful! A QR code will be generated by the event organizer. Once generated, you will be able to print your registration sheet.'
                        ];
                        
                        $this->redirect('user/registrations');
                    } else {
                        // For paid shows, redirect to payment page
                        
                        // Set a flash message to inform the user about QR code generation
                        $_SESSION['flash_message'] = [
                            'type' => 'info',
                            'message' => 'Registration successful! After payment is complete, a QR code will be generated by the event organizer. Once generated, you will be able to print your registration sheet.'
                        ];
                        
                        $this->redirect('payment/process/' . $registrationId);
                    }
                } else {
                    $this->redirect('home/error/Failed%20to%20create%20registration');
                }
            } else {
                // If there are errors, show the form again with errors
                $data = [
                    'title' => 'Register for ' . $show->name,
                    'show' => $show,
                    'vehicles' => $vehicles,
                    'categories' => $categories,
                    'payment_methods' => $paymentMethods,
                    'errors' => $errors,
                    'vehicle_id' => $vehicleId,
                    'category_id' => $categoryId,
                    'payment_method_id' => $paymentMethodId,
                    'template' => $template,
                    'template_source' => $templateSource
                ];
                
                $this->view('registration/register', $data);
            }
        } else {
            // Init data
            $data = [
                'title' => 'Register for ' . $show->name,
                'show' => $show,
                'vehicles' => $vehicles,
                'categories' => $categories,
                'payment_methods' => $paymentMethods,
                'errors' => [],
                'template' => $template,
                'template_source' => $templateSource
            ];
            
            // Load view
            $this->view('registration/register', $data);
        }
    }
    
    /**
     * Cancel a registration
     * 
     * @param int $id Registration ID
     */
    public function cancel($id) {
        // Ensure user is logged in
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('auth/login');
            return;
        }
        
        // Get registration
        $registration = $this->registrationModel->getRegistrationById($id);
        
        if (!$registration) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Verify user owns the registration
        $userId = $_SESSION['user_id'];
        if ($registration->owner_id != $userId && $_SESSION['user_role'] != 'admin') {
            $this->redirect('home/error/You%20do%20not%20have%20permission%20to%20cancel%20this%20registration');
            return;
        }
        
        // Check if registration can be cancelled
        $show = $this->showModel->getShowById($registration->show_id);
        if (!$show || $show->status == 'completed') {
            $this->redirect('home/error/This%20registration%20cannot%20be%20cancelled');
            return;
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Cancel registration
            $updateData = [
                'id' => $id,
                'status' => 'cancelled'
            ];
            
            if ($this->registrationModel->updateRegistration($updateData)) {
                // Set success message
                setFlashMessage('success', 'Registration cancelled successfully');
                $this->redirect('user/registrations');
            } else {
                $this->redirect('home/error/Failed%20to%20cancel%20registration');
            }
        } else {
            // Init data
            $data = [
                'title' => 'Cancel Registration',
                'registration' => $registration,
                'show' => $show
            ];
            
            // Load view
            $this->view('registration/cancel', $data);
        }
    }
}