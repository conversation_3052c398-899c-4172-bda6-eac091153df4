<?php
/**
 * Debug script for the cleanup test users functionality
 * 
 * This script helps debug why the cleanup function isn't working by:
 * 1. Checking what users exist in the database
 * 2. Testing the cleanup queries step by step
 * 3. Showing detailed information about the matching process
 */

// Load configuration
require_once 'config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once 'core/Database.php';
require_once 'core/Auth.php';

// Include the test user generator
require_once 'database/test_data/generate_test_users.php';

echo "<h1>Debug Cleanup Test Users</h1>";
echo "<p>Debugging why the cleanup function isn't working...</p>";

try {
    $db = new Database();
    $generator = new TestUserGenerator();
    
    echo "<h2>Step 1: Check total users in database</h2>";
    $db->query("SELECT COUNT(*) as count FROM users");
    $totalResult = $db->single();
    $totalUsers = $totalResult->count ?? 0;
    echo "<p><strong>Total users in database:</strong> {$totalUsers}</p>";
    
    if ($totalUsers == 0) {
        echo "<p style='color: orange;'>No users found in database. Generate some test users first.</p>";
        echo "<p><a href='database/test_data/generate_test_users.php?action=generate&count=10'>Generate 10 test users</a></p>";
    } else {
        echo "<h2>Step 2: Check users with test domains</h2>";
        $testDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'example.com'];
        $domainConditions = array_map(function($domain) {
            return "email LIKE '%@{$domain}'";
        }, $testDomains);
        $domainClause = "(" . implode(' OR ', $domainConditions) . ")";
        
        $db->query("SELECT COUNT(*) as count FROM users WHERE " . $domainClause);
        $domainResult = $db->single();
        $domainUsers = $domainResult->count ?? 0;
        echo "<p><strong>Users with test domains:</strong> {$domainUsers}</p>";
        
        if ($domainUsers > 0) {
            echo "<h3>Sample users with test domains:</h3>";
            $db->query("SELECT id, name, email, created_at FROM users WHERE " . $domainClause . " LIMIT 10");
            $samples = $db->resultSet();
            
            if (!empty($samples)) {
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>ID</th><th>Name</th><th>Email</th><th>Created</th></tr>";
                foreach ($samples as $user) {
                    echo "<tr>";
                    echo "<td>{$user->id}</td>";
                    echo "<td>{$user->name}</td>";
                    echo "<td>{$user->email}</td>";
                    echo "<td>{$user->created_at}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
            echo "<h2>Step 3: Test regex pattern matching</h2>";
            $regexPattern = "^[A-Za-z]+ [A-Za-z]+[0-9]*$";
            $fullClause = $domainClause . " AND name REGEXP '" . $regexPattern . "'";
            
            echo "<p><strong>Regex pattern:</strong> {$regexPattern}</p>";
            echo "<p><strong>Full WHERE clause:</strong> " . htmlspecialchars($fullClause) . "</p>";
            
            $db->query("SELECT COUNT(*) as count FROM users WHERE " . $fullClause);
            $regexResult = $db->single();
            $regexUsers = $regexResult->count ?? 0;
            echo "<p><strong>Users matching regex pattern:</strong> {$regexUsers}</p>";
            
            if ($regexUsers > 0) {
                echo "<h3>Users that match the regex pattern:</h3>";
                $db->query("SELECT id, name, email FROM users WHERE " . $fullClause . " LIMIT 5");
                $regexSamples = $db->resultSet();
                
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>ID</th><th>Name</th><th>Email</th></tr>";
                foreach ($regexSamples as $user) {
                    echo "<tr>";
                    echo "<td>{$user->id}</td>";
                    echo "<td>{$user->name}</td>";
                    echo "<td>{$user->email}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p style='color: orange;'>No users match the regex pattern. Let's test individual names:</p>";
                
                echo "<h3>Testing individual names against regex:</h3>";
                foreach ($samples as $user) {
                    $matches = preg_match('/^[A-Za-z]+ [A-Za-z]+[0-9]*$/', $user->name);
                    $status = $matches ? "✓ MATCHES" : "✗ NO MATCH";
                    echo "<p><strong>{$user->name}</strong> - {$status}</p>";
                }
            }
            
            echo "<h2>Step 4: Test cleanup functions</h2>";
            echo "<p><a href='?action=test_simple'>Test Simple Cleanup (domain-based only)</a></p>";
            echo "<p><a href='?action=test_pattern'>Test Pattern Cleanup (with regex)</a></p>";
            
        } else {
            echo "<p style='color: orange;'>No users with test domains found.</p>";
        }
    }
    
    // Handle test actions
    if (isset($_GET['action'])) {
        echo "<hr>";
        
        if ($_GET['action'] === 'test_simple') {
            echo "<h2>Testing Simple Cleanup</h2>";
            echo "<pre>";
            $deleted = $generator->cleanupTestUsersSimple(true);
            echo "</pre>";
            echo "<p><strong>Result:</strong> Deleted {$deleted} users</p>";
            
        } elseif ($_GET['action'] === 'test_pattern') {
            echo "<h2>Testing Pattern Cleanup</h2>";
            echo "<pre>";
            $deleted = $generator->cleanupTestUsers(true);
            echo "</pre>";
            echo "<p><strong>Result:</strong> Deleted {$deleted} users</p>";
        }
        
        echo "<p><a href='?'>← Back to Debug Info</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>✗ Error occurred:</strong> " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<p><a href='database/test_data/generate_test_users.php'>← Back to Test User Generator</a></p>";
