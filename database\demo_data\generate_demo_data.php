<?php
/**
 * Demo Data Generator for Events and Shows Management System
 * 
 * This script generates comprehensive demo data for showcasing the platform to potential customers.
 * Creates realistic shows, events, users, vehicles, registrations, payments, voting, and scoring data.
 * 
 * FEATURES:
 * - 10 realistic car shows with professional descriptions
 * - Coordinators, judges, and participants
 * - Vehicle registrations with detailed descriptions
 * - Complete judging system with scores and comments
 * - Fan voting data
 * - Payment records and transactions
 * - Show categories and judging metrics
 * - Professional, sales-ready demo data
 * 
 * SAFE DOMAINS: Uses test domains (gmai1.com, etc.) to protect real users
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once '../../models/ImageEditorModel.php';

class DemoDataGenerator {
    private $db;
    private $batchSize = 50;
    private $generatedData = [];

    // Safe test domains to prevent affecting real users
    private $testDomains = ['gmai1.com', 'yaho0.com', 'hotmai1.com', 'out1ook.com', 'test-example.com'];

    // Free image sources (using placeholder services and free stock photos)
    private $vehicleImageUrls = [
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop', // Red sports car
        'https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=800&h=600&fit=crop', // Classic car
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop', // Muscle car
        'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=800&h=600&fit=crop', // Porsche
        'https://images.unsplash.com/photo-1580273916550-e323be2ae537?w=800&h=600&fit=crop', // Ferrari
        'https://images.unsplash.com/photo-1555215695-3004980ad54e?w=800&h=600&fit=crop', // BMW
        'https://images.unsplash.com/photo-1542362567-b07e54358753?w=800&h=600&fit=crop', // Lamborghini
        'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop', // Jaguar
        'https://images.unsplash.com/photo-1525609004556-c46c7d6cf023?w=800&h=600&fit=crop', // Corvette
        'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=800&h=600&fit=crop', // Mercedes
        'https://images.unsplash.com/photo-1549399542-7e3f8b79c341?w=800&h=600&fit=crop', // Shelby Cobra
        'https://images.unsplash.com/photo-1494905998402-395d579af36f?w=800&h=600&fit=crop', // Plymouth
        'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=800&h=600&fit=crop', // Nissan GTR
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop', // Toyota Supra
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop', // Honda NSX
        'https://images.unsplash.com/photo-1544829099-b9a0c5303bea?w=800&h=600&fit=crop', // McLaren
        'https://images.unsplash.com/photo-1551522435-a13afa10f103?w=800&h=600&fit=crop', // Ford GT
        'https://images.unsplash.com/photo-1563720223185-11003d516935?w=800&h=600&fit=crop', // Aston Martin
        'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=800&h=600&fit=crop', // Maserati
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop'  // Alfa Romeo
    ];

    private $showImageUrls = [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=1200&h=800&fit=crop', // Car show venue
        'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=1200&h=800&fit=crop', // Car exhibition
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop', // Car meet
        'https://images.unsplash.com/photo-1551522435-a13afa10f103?w=1200&h=800&fit=crop', // Racing event
        'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=1200&h=800&fit=crop', // Classic car show
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=1200&h=800&fit=crop', // Exotic car display
        'https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=1200&h=800&fit=crop', // Vintage rally
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1200&h=800&fit=crop', // Muscle car expo
        'https://images.unsplash.com/photo-1580273916550-e323be2ae537?w=1200&h=800&fit=crop', // Import tuner show
        'https://images.unsplash.com/photo-1555215695-3004980ad54e?w=1200&h=800&fit=crop'  // Supercar spectacular
    ];

    public function __construct() {
        $this->db = new Database();

        // Set execution limits for large data generation
        ini_set('max_execution_time', 600); // 10 minutes
        ini_set('memory_limit', '512M');

        // Create upload directories if they don't exist
        $this->createUploadDirectories();
    }
    
    /**
     * Generate complete demo dataset
     */
    public function generateCompleteDemo($verbose = true) {
        if ($verbose) {
            echo "🚀 Generating Complete Demo Dataset for Events and Shows Management System\n";
            echo "================================================================\n\n";
        }
        
        $startTime = microtime(true);
        
        try {
            // Step 1: Generate Users (Coordinators, Judges, Participants)
            if ($verbose) echo "👥 Step 1: Generating Users...\n";
            $this->generateUsers($verbose);
            
            // Step 2: Generate Shows
            if ($verbose) echo "\n🏆 Step 2: Generating Car Shows...\n";
            $this->generateShows($verbose);
            
            // Step 3: Generate Show Categories and Judging Metrics
            if ($verbose) echo "\n📋 Step 3: Generating Categories and Judging Metrics...\n";
            $this->generateCategoriesAndMetrics($verbose);
            
            // Step 4: Generate Vehicles
            if ($verbose) echo "\n🚗 Step 4: Generating Vehicles...\n";
            $this->generateVehicles($verbose);
            
            // Step 5: Generate Registrations
            if ($verbose) echo "\n📝 Step 5: Generating Registrations...\n";
            $this->generateRegistrations($verbose);
            
            // Step 6: Generate Payments
            if ($verbose) echo "\n💳 Step 6: Generating Payment Records...\n";
            $this->generatePayments($verbose);

            // Step 7: Assign Judges to Categories
            if ($verbose) echo "\n👨‍⚖️ Step 7: Assigning Judges to Categories...\n";
            $this->assignJudgesToCategories($verbose);

            // Step 8: Generate Judging Scores
            if ($verbose) echo "\n⭐ Step 8: Generating Judging Scores...\n";
            $this->generateJudgingScores($verbose);
            
            // Step 9: Generate Fan Votes
            if ($verbose) echo "\n👍 Step 9: Generating Fan Votes...\n";
            $this->generateFanVotes($verbose);

            // Step 10: Download and Add Images (including banner images)
            if ($verbose) echo "\n📸 Step 10: Downloading and Adding Images...\n";
            $this->generateImages($verbose);

            $totalTime = round(microtime(true) - $startTime, 2);
            
            if ($verbose) {
                echo "\n✅ Demo Data Generation Complete!\n";
                echo "================================================================\n";
                echo "Total Generation Time: {$totalTime} seconds\n";
                echo "\nGenerated Data Summary:\n";
                foreach ($this->generatedData as $type => $count) {
                    echo "- {$type}: {$count}\n";
                }
                echo "\n🎯 Your demo site is now ready to showcase to potential customers!\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            if ($verbose) {
                echo "\n❌ Error generating demo data: " . $e->getMessage() . "\n";
                echo $e->getTraceAsString() . "\n";
            }
            return false;
        }
    }
    
    /**
     * Generate realistic users for the demo
     */
    private function generateUsers($verbose = true) {
        $users = [
            // Coordinators
            ['name' => 'Michael Rodriguez', 'email' => '<EMAIL>', 'role' => 'coordinator', 'phone' => '(*************'],
            ['name' => 'Sarah Thompson', 'email' => '<EMAIL>', 'role' => 'coordinator', 'phone' => '(*************'],
            ['name' => 'David Chen', 'email' => '<EMAIL>', 'role' => 'coordinator', 'phone' => '(*************'],
            
            // Judges
            ['name' => 'Robert Wilson', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            ['name' => 'Jennifer Davis', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            ['name' => 'Mark Johnson', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            ['name' => 'Lisa Anderson', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            
            // Participants
            ['name' => 'James Miller', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Maria Garcia', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Christopher Brown', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Amanda Taylor', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Daniel Martinez', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Jessica White', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Kevin Lee', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Rachel Green', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Steven Clark', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Nicole Adams', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Brian Turner', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Michelle Scott', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Ryan Phillips', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Stephanie King', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
        ];
        
        $cities = ['Los Angeles', 'Miami', 'Austin', 'Phoenix', 'Atlanta', 'Denver', 'Seattle', 'Nashville', 'Charlotte', 'Las Vegas'];
        $states = ['CA', 'FL', 'TX', 'AZ', 'GA', 'CO', 'WA', 'TN', 'NC', 'NV'];
        
        $generated = 0;
        foreach ($users as $index => $userData) {
            $city = $cities[array_rand($cities)];
            $state = $states[array_rand($states)];
            $address = (1000 + $index) . ' Demo Street';
            $zip = sprintf('%05d', rand(10000, 99999));
            
            $sql = "INSERT INTO users (name, email, password, role, status, phone, address, city, state, zip, created_at) 
                    VALUES (?, ?, ?, ?, 'active', ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->execute([
                $userData['name'],
                $userData['email'],
                password_hash('demo123', PASSWORD_DEFAULT),
                $userData['role'],
                $userData['phone'],
                $address,
                $city,
                $state,
                $zip
            ]);
            
            $generated++;
            
            if ($verbose && $generated % 5 == 0) {
                echo "  Generated {$generated} users...\n";
            }
        }
        
        $this->generatedData['Users'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} demo users\n";
    }
    
    /**
     * Clean up demo data (removes all demo-related data comprehensively)
     */
    public function cleanupDemoData($verbose = true) {
        if ($verbose) {
            echo "🧹 Comprehensive Demo Data Cleanup...\n";
            echo "=====================================\n";
        }

        $deletedCounts = [];

        try {
            $domainConditions = array_map(function($domain) {
                return "email LIKE '%@{$domain}'";
            }, $this->testDomains);
            $whereClause = "(" . implode(' OR ', $domainConditions) . ")";

            // Get demo user IDs first
            $this->db->query("SELECT id FROM users WHERE " . $whereClause);
            $demoUsers = $this->db->resultSet();
            $demoUserIds = array_column($demoUsers, 'id');

            if (empty($demoUserIds)) {
                if ($verbose) {
                    echo "  ℹ️ No demo users found, checking for orphaned demo data...\n";
                }
            } else {
                if ($verbose) {
                    echo "  📊 Found " . count($demoUserIds) . " demo users to clean up\n";
                }
            }

            // Clean up in reverse order of dependencies

            // 1. Clean up images first
            if ($verbose) echo "  🖼️ Cleaning up demo images...\n";
            $this->cleanupDemoImages($demoUserIds, $verbose);

            // 2. Clean up fan votes (only for demo shows)
            if ($verbose) echo "  👍 Cleaning up fan votes...\n";
            $deletedCounts['fan_votes'] = $this->cleanupDemoFanVotes($verbose);

            // 3. Clean up judging scores (actual tables used) - only for demo shows/users
            if ($verbose) echo "  ⭐ Cleaning up judging scores...\n";
            $deletedCounts = array_merge($deletedCounts, $this->cleanupDemoScoringData($demoUserIds, $verbose));

            // 4. Clean up payments (targeted - only demo registration payments)
            if ($verbose) echo "  💳 Cleaning up payments...\n";
            $deletedCounts['payments'] = $this->cleanupDemoPayments($verbose);

            // 5. Clean up judge assignments and scores
            if ($verbose) echo "  👨‍⚖️ Cleaning up judge assignments and scores...\n";
            $deletedCounts = array_merge($deletedCounts, $this->cleanupJudgingData($verbose));

            // 6. Clean up registrations
            if ($verbose) echo "  📝 Cleaning up registrations...\n";
            if (!empty($demoUserIds)) {
                // Check if registrations uses user_id or owner_id
                $this->db->query("DESCRIBE registrations");
                $regColumns = $this->db->resultSet();
                $regColumnNames = array_column($regColumns, 'Field');
                $userColumn = in_array('user_id', $regColumnNames) ? 'user_id' : 'owner_id';

                $deletedCounts['registrations'] = $this->cleanupTableData('registrations', [$userColumn => $demoUserIds], $verbose);
            }

            // 6. Clean up vehicles
            if ($verbose) echo "  🚗 Cleaning up vehicles...\n";
            if (!empty($demoUserIds)) {
                $deletedCounts['vehicles'] = $this->cleanupTableData('vehicles', ['owner_id' => $demoUserIds], $verbose);
            }

            // 7. Clean up show categories and judging metrics
            if ($verbose) echo "  📋 Cleaning up show categories and judging metrics...\n";
            $deletedCounts['judging_metrics'] = $this->cleanupTableData('judging_metrics', [], $verbose);
            $deletedCounts['show_categories'] = $this->cleanupTableData('show_categories', [], $verbose);

            // 8. Clean up shows (coordinators are demo users + pattern matching)
            if ($verbose) echo "  🏆 Cleaning up shows...\n";
            $deletedCounts['shows'] = $this->cleanupDemoShows($demoUserIds, $verbose);

            // 9. Finally, clean up demo users
            if ($verbose) echo "  👥 Cleaning up demo users...\n";
            if (!empty($demoUserIds)) {
                $this->db->query("DELETE FROM users WHERE " . $whereClause);
                $deletedCounts['users'] = $this->db->execute() ? $this->db->rowCount() : 0;
            }

            // 10. Clean up any orphaned demo data by pattern matching
            if ($verbose) echo "  🧹 Cleaning up orphaned demo data...\n";
            $this->cleanupOrphanedDemoData($verbose);

            if ($verbose) {
                echo "\n✅ Comprehensive Cleanup Complete!\n";
                echo "===================================\n";
                foreach ($deletedCounts as $table => $count) {
                    if ($count > 0) {
                        echo "  - {$table}: {$count} records deleted\n";
                    }
                }

                $totalDeleted = array_sum($deletedCounts);
                echo "\n🎯 Total: {$totalDeleted} demo records removed\n";
            }

            return array_sum($deletedCounts);

        } catch (Exception $e) {
            if ($verbose) {
                echo "  ❌ Error during comprehensive cleanup: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Generate realistic car shows
     */
    private function generateShows($verbose = true) {
        // Get coordinator IDs
        $this->db->query("SELECT id FROM users WHERE role = 'coordinator' AND email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' LIMIT 3");
        $coordinators = $this->db->resultSet();

        $shows = [
            [
                'name' => 'Southern California Classic Car Showcase',
                'description' => 'Join us for the premier classic car event on the West Coast! Featuring vintage automobiles from 1920-1980, this prestigious show attracts collectors and enthusiasts from across the nation. Professional judging, awards ceremony, and family-friendly activities. Food trucks, live music, and vendor booths available.',
                'location' => 'Exposition Park, Los Angeles, CA',
                'start_date' => '2025-08-15',
                'end_date' => '2025-08-17',
                'registration_fee' => 45.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Miami Beach Exotic Car Festival',
                'description' => 'Experience the ultimate luxury car event in beautiful Miami Beach! Showcasing supercars, hypercars, and exotic vehicles from around the world. VIP experiences available. Professional photography, media coverage, and networking opportunities with industry leaders.',
                'location' => 'Miami Beach Convention Center, Miami, FL',
                'start_date' => '2025-09-20',
                'end_date' => '2025-09-22',
                'registration_fee' => 75.00,
                'listing_fee' => 35.00
            ],
            [
                'name' => 'Texas Muscle Car Madness',
                'description' => 'Rev up for the biggest muscle car event in Texas! Featuring American muscle from the golden era - Mustangs, Camaros, Challengers, and more. Drag racing demonstrations, burnout contests, and live entertainment. BBQ food vendors and family activities.',
                'location' => 'Circuit of the Americas, Austin, TX',
                'start_date' => '2025-10-05',
                'end_date' => '2025-10-06',
                'registration_fee' => 35.00,
                'listing_fee' => 20.00
            ],
            [
                'name' => 'Arizona Desert Classic Concours',
                'description' => 'An elegant concours d\'elegance in the stunning Arizona desert. Featuring meticulously restored classics, rare European sports cars, and American luxury vehicles. Professional judging by certified experts. Awards banquet and charity auction.',
                'location' => 'Scottsdale Civic Center, Scottsdale, AZ',
                'start_date' => '2025-11-10',
                'end_date' => '2025-11-12',
                'registration_fee' => 65.00,
                'listing_fee' => 30.00
            ],
            [
                'name' => 'Atlanta Import Tuner Expo',
                'description' => 'The Southeast\'s premier import and tuner car show! Featuring modified Japanese, European, and domestic vehicles. Dyno competitions, sound-off contests, and drift demonstrations. Vendor marketplace with the latest aftermarket parts and accessories.',
                'location' => 'Georgia World Congress Center, Atlanta, GA',
                'start_date' => '2025-07-25',
                'end_date' => '2025-07-27',
                'registration_fee' => 40.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Rocky Mountain Vintage Rally',
                'description' => 'A scenic mountain rally featuring vintage and classic vehicles. Driving tours through Colorado\'s beautiful landscapes, technical sessions, and social events. Perfect for vintage car enthusiasts who love to drive their classics.',
                'location' => 'Denver Convention Center, Denver, CO',
                'start_date' => '2025-08-30',
                'end_date' => '2025-09-01',
                'registration_fee' => 55.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Pacific Northwest Euro Fest',
                'description' => 'Celebrating European automotive excellence in the beautiful Pacific Northwest. BMW, Mercedes-Benz, Audi, Porsche, and more. Technical seminars, parts swap meet, and scenic drives. Coffee and craft beer garden.',
                'location' => 'Seattle Center, Seattle, WA',
                'start_date' => '2025-09-15',
                'end_date' => '2025-09-16',
                'registration_fee' => 50.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Music City Hot Rod Nationals',
                'description' => 'Nashville\'s hottest rod and custom car event! Traditional hot rods, street rods, and custom builds. Live country music, pin-up contests, and vintage vendor booths. Family-friendly with activities for all ages.',
                'location' => 'Nashville Fairgrounds, Nashville, TN',
                'start_date' => '2025-10-20',
                'end_date' => '2025-10-21',
                'registration_fee' => 30.00,
                'listing_fee' => 20.00
            ],
            [
                'name' => 'Charlotte Motor Speedway Car Show',
                'description' => 'Experience automotive excellence at the legendary Charlotte Motor Speedway! All makes and models welcome. Track tours, racing simulators, and meet-and-greets with NASCAR drivers. Professional photography and awards ceremony.',
                'location' => 'Charlotte Motor Speedway, Charlotte, NC',
                'start_date' => '2025-11-25',
                'end_date' => '2025-11-26',
                'registration_fee' => 45.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Las Vegas Strip Supercar Spectacular',
                'description' => 'The most glamorous supercar event in Las Vegas! Featuring the world\'s most exclusive and expensive vehicles. Red carpet arrivals, celebrity appearances, and VIP experiences. Night cruise down the famous Las Vegas Strip.',
                'location' => 'Las Vegas Convention Center, Las Vegas, NV',
                'start_date' => '2025-12-10',
                'end_date' => '2025-12-12',
                'registration_fee' => 100.00,
                'listing_fee' => 50.00
            ]
        ];

        $generated = 0;
        foreach ($shows as $index => $showData) {
            $coordinatorId = $coordinators[$index % count($coordinators)]->id;

            // Calculate registration dates (30-60 days before show)
            $startDate = new DateTime($showData['start_date']);
            $regStart = clone $startDate;
            $regStart->modify('-60 days');
            $regEnd = clone $startDate;
            $regEnd->modify('-7 days');

            $sql = "INSERT INTO shows (name, description, location, start_date, end_date, registration_start, registration_end,
                    coordinator_id, status, fan_voting_enabled, registration_fee, listing_fee, listing_paid, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'published', 1, ?, ?, 1, NOW())";

            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->execute([
                $showData['name'],
                $showData['description'],
                $showData['location'],
                $showData['start_date'],
                $showData['end_date'],
                $regStart->format('Y-m-d'),
                $regEnd->format('Y-m-d'),
                $coordinatorId,
                $showData['registration_fee'],
                $showData['listing_fee']
            ]);

            $generated++;

            if ($verbose && $generated % 3 == 0) {
                echo "  Generated {$generated} shows...\n";
            }
        }

        $this->generatedData['Shows'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} professional car shows\n";
    }

    /**
     * Generate show categories and judging metrics
     */
    private function generateCategoriesAndMetrics($verbose = true) {
        // Get all show IDs
        $this->db->query("SELECT id FROM shows ORDER BY id");
        $shows = $this->db->resultSet();

        $categories = [
            ['name' => 'Classic American', 'description' => 'American classics from 1920-1980'],
            ['name' => 'European Sports Cars', 'description' => 'European sports and luxury vehicles'],
            ['name' => 'Muscle Cars', 'description' => 'American muscle cars from the golden era'],
            ['name' => 'Import Tuners', 'description' => 'Modified import and tuner vehicles'],
            ['name' => 'Exotic Supercars', 'description' => 'High-end exotic and supercar vehicles'],
            ['name' => 'Hot Rods & Customs', 'description' => 'Traditional hot rods and custom builds'],
            ['name' => 'Vintage Racing', 'description' => 'Historic racing and competition vehicles']
        ];

        $metrics = [
            ['name' => 'Exterior Condition', 'description' => 'Paint, body, chrome, and overall exterior appearance', 'max_score' => 25, 'weight' => 1.0],
            ['name' => 'Interior Quality', 'description' => 'Seats, dashboard, trim, and interior components', 'max_score' => 20, 'weight' => 0.8],
            ['name' => 'Engine Bay', 'description' => 'Engine cleanliness, originality, and presentation', 'max_score' => 20, 'weight' => 0.9],
            ['name' => 'Undercarriage', 'description' => 'Chassis, suspension, exhaust, and underside condition', 'max_score' => 15, 'weight' => 0.7],
            ['name' => 'Authenticity', 'description' => 'Period correctness and original components', 'max_score' => 10, 'weight' => 1.2],
            ['name' => 'Craftsmanship', 'description' => 'Quality of restoration or modification work', 'max_score' => 10, 'weight' => 1.1]
        ];

        $categoriesGenerated = 0;
        $metricsGenerated = 0;

        foreach ($shows as $show) {
            // Generate 4-5 categories per show
            $showCategories = array_slice($categories, 0, rand(4, 5));

            foreach ($showCategories as $index => $categoryData) {
                $sql = "INSERT INTO show_categories (show_id, name, description, display_order, created_at)
                        VALUES (?, ?, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $show->id,
                    $categoryData['name'],
                    $categoryData['description'],
                    $index + 1
                ]);

                $categoriesGenerated++;
            }

            // Generate judging metrics for each show
            foreach ($metrics as $index => $metricData) {
                $sql = "INSERT INTO judging_metrics (show_id, name, description, max_score, weight, display_order, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $show->id,
                    $metricData['name'],
                    $metricData['description'],
                    $metricData['max_score'],
                    $metricData['weight'],
                    $index + 1
                ]);

                $metricsGenerated++;
            }
        }

        $this->generatedData['Categories'] = $categoriesGenerated;
        $this->generatedData['Judging Metrics'] = $metricsGenerated;
        if ($verbose) echo "  ✅ Generated {$categoriesGenerated} categories and {$metricsGenerated} judging metrics\n";
    }

    /**
     * Generate realistic vehicles
     */
    private function generateVehicles($verbose = true) {
        // Get user IDs (participants)
        $this->db->query("SELECT id FROM users WHERE role = 'user' AND (email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com')");
        $users = $this->db->resultSet();

        $vehicles = [
            ['make' => 'Ford', 'model' => 'Mustang GT', 'year' => 1967, 'color' => 'Candy Apple Red', 'description' => 'Fully restored 1967 Mustang GT with 390 V8 engine. Numbers matching, concours quality restoration with original interior and chrome. Recent frame-off restoration completed in 2023.'],
            ['make' => 'Chevrolet', 'model' => 'Camaro SS', 'year' => 1969, 'color' => 'Rally Green', 'description' => 'Stunning 1969 Camaro SS with 396 big block. Professional restoration with period-correct Rally wheels. Show quality paint and interior. Documented with build sheet and Protect-O-Plate.'],
            ['make' => 'Dodge', 'model' => 'Challenger R/T', 'year' => 1970, 'color' => 'Plum Crazy Purple', 'description' => 'Rare 1970 Challenger R/T 440 Six Pack. Rotisserie restoration with correct Plum Crazy paint. Numbers matching drivetrain with Shaker hood. Investment grade muscle car.'],
            ['make' => 'Porsche', 'model' => '911 Turbo', 'year' => 1987, 'color' => 'Guards Red', 'description' => 'Exceptional 1987 Porsche 911 Turbo with only 45,000 original miles. Maintained by Porsche specialists, all service records available. Original paint, leather interior in excellent condition.'],
            ['make' => 'Ferrari', 'model' => '308 GTS', 'year' => 1979, 'color' => 'Rosso Corsa', 'description' => 'Beautiful 1979 Ferrari 308 GTS with removable targa top. Recent major service including timing belt and clutch. Stunning example of this iconic 1980s supercar with proper documentation.'],
            ['make' => 'BMW', 'model' => 'M3', 'year' => 1988, 'color' => 'Alpine White', 'description' => 'Pristine 1988 BMW E30 M3 with S14 engine. One of the most sought-after BMWs ever made. Completely stock with original BBS wheels. Garage kept with meticulous maintenance records.'],
            ['make' => 'Lamborghini', 'model' => 'Countach', 'year' => 1985, 'color' => 'Bianco Polo', 'description' => 'Iconic 1985 Lamborghini Countach LP5000 QV. The poster car of the 1980s in stunning white. Recent service by Lamborghini specialists. Extremely rare and appreciating classic supercar.'],
            ['make' => 'Jaguar', 'model' => 'E-Type', 'year' => 1963, 'color' => 'British Racing Green', 'description' => 'Gorgeous 1963 Jaguar E-Type Series 1 roadster. Enzo Ferrari called it "the most beautiful car ever made." Matching numbers with extensive restoration documentation.'],
            ['make' => 'Corvette', 'model' => 'Stingray', 'year' => 1963, 'color' => 'Riverside Red', 'description' => 'Split-window 1963 Corvette Stingray coupe. One year only design with fuel injection. Concours restoration with correct paint and interior. NCRS Top Flight award winner.'],
            ['make' => 'Mercedes-Benz', 'model' => '300SL Gullwing', 'year' => 1955, 'color' => 'Silver Metallic', 'description' => 'Legendary 1955 Mercedes-Benz 300SL Gullwing coupe. One of the most desirable classics ever built. Matching numbers with comprehensive restoration. Museum quality example.'],
            ['make' => 'Shelby', 'model' => 'Cobra 427', 'year' => 1965, 'color' => 'Guardsman Blue', 'description' => 'Authentic 1965 Shelby Cobra 427 S/C. The ultimate American sports car with massive 427 side-oiler engine. Documented history with Shelby American registry verification.'],
            ['make' => 'Plymouth', 'model' => 'Barracuda', 'year' => 1970, 'color' => 'In-Violet', 'description' => 'Rare 1970 Plymouth \'Cuda with 440 Six Pack engine. High Impact In-Violet paint with black interior. Numbers matching with broadcast sheet. Professionally restored to concours standards.'],
            ['make' => 'Nissan', 'model' => 'Skyline GT-R', 'year' => 1995, 'color' => 'Midnight Purple', 'description' => 'JDM 1995 Nissan Skyline GT-R R33 in rare Midnight Purple. RB26DETT twin-turbo engine with all-wheel drive. Recently imported and federalized. Enthusiast owned with modifications.'],
            ['make' => 'Toyota', 'model' => 'Supra Turbo', 'year' => 1994, 'color' => 'Renaissance Red', 'description' => 'Pristine 1994 Toyota Supra Turbo with 6-speed manual transmission. Stock 2JZ-GTE engine with massive potential. Garage kept with only 65,000 original miles.'],
            ['make' => 'Honda', 'model' => 'NSX', 'year' => 1991, 'color' => 'Formula Red', 'description' => 'Exceptional 1991 Acura NSX in Formula Red with black interior. Honda\'s supercar with mid-mounted VTEC V6. Meticulously maintained with all service records. Future classic.'],
            ['make' => 'McLaren', 'model' => 'F1', 'year' => 1995, 'color' => 'Carbon Fiber', 'description' => 'Ultra-rare 1995 McLaren F1 road car. Only 106 ever built. Central driving position with gold-lined engine bay. The ultimate analog supercar and automotive masterpiece.'],
            ['make' => 'Ford', 'model' => 'GT40', 'year' => 1966, 'color' => 'Gulf Racing Blue', 'description' => 'Legendary 1966 Ford GT40 Mk I in iconic Gulf Racing livery. Le Mans-winning design with 289 V8 engine. Documented racing history and provenance. Investment grade race car.'],
            ['make' => 'Aston Martin', 'model' => 'DB5', 'year' => 1964, 'color' => 'Silver Birch', 'description' => 'Iconic 1964 Aston Martin DB5 in Silver Birch with red leather interior. The James Bond car with timeless British elegance. Matching numbers with comprehensive service history.'],
            ['make' => 'Maserati', 'model' => 'Ghibli', 'year' => 1969, 'color' => 'Blu Sera', 'description' => 'Stunning 1969 Maserati Ghibli with 4.7L V8 engine. Giugiaro-designed masterpiece with incredible Italian styling. Recent restoration by Maserati specialists.'],
            ['make' => 'Alfa Romeo', 'model' => 'Spider', 'year' => 1967, 'color' => 'Alfa Red', 'description' => 'Beautiful 1967 Alfa Romeo Spider Duetto. The Graduate car with timeless Italian design. Matching numbers with recent mechanical restoration. Perfect weekend touring car.']
        ];

        $generated = 0;
        foreach ($vehicles as $index => $vehicleData) {
            $ownerId = $users[$index % count($users)]->id;
            $licensePlate = 'DEMO' . sprintf('%03d', $index + 1);
            $vin = 'DEMO' . str_pad($index + 1, 13, '0', STR_PAD_LEFT);

            $sql = "INSERT INTO vehicles (owner_id, make, model, year, color, license_plate, vin, description, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->execute([
                $ownerId,
                $vehicleData['make'],
                $vehicleData['model'],
                $vehicleData['year'],
                $vehicleData['color'],
                $licensePlate,
                $vin,
                $vehicleData['description']
            ]);

            $generated++;

            if ($verbose && $generated % 5 == 0) {
                echo "  Generated {$generated} vehicles...\n";
            }
        }

        $this->generatedData['Vehicles'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} stunning demo vehicles\n";
    }

    /**
     * Generate vehicle registrations for shows
     */
    private function generateRegistrations($verbose = true) {
        // Get shows, vehicles, and categories
        $this->db->query("SELECT id FROM shows ORDER BY id");
        $shows = $this->db->resultSet();

        $this->db->query("SELECT id, owner_id FROM vehicles ORDER BY id");
        $vehicles = $this->db->resultSet();

        $generated = 0;

        foreach ($shows as $show) {
            // Get categories for this show
            $this->db->query("SELECT id FROM show_categories WHERE show_id = :show_id");
            $this->db->bind(':show_id', $show->id);
            $categories = $this->db->resultSet();

            if (empty($categories)) continue;

            // Register 8-12 vehicles per show
            $vehiclesToRegister = array_slice($vehicles, 0, rand(8, 12));

            foreach ($vehiclesToRegister as $vehicle) {
                $categoryId = $categories[array_rand($categories)]->id;
                $registrationNumber = 'RER-' . str_pad($generated + 1, 6, '0', STR_PAD_LEFT);
                $displayNumber = sprintf('%03d', ($generated % 999) + 1);

                // First check what columns exist in the registrations table
                $this->db->query("DESCRIBE registrations");
                $columns = $this->db->resultSet();
                $columnNames = array_column($columns, 'Field');

                // Determine which user column to use
                $userColumn = in_array('user_id', $columnNames) ? 'user_id' : 'owner_id';

                // Check if payment_status column exists and what values it accepts
                $paymentStatusColumn = in_array('payment_status', $columnNames) ? 'payment_status' : null;
                $feeColumn = in_array('fee', $columnNames) ? 'fee' : 'payment_amount';

                // Build the SQL based on available columns
                $sqlColumns = ['show_id', 'vehicle_id', $userColumn, 'category_id', 'status'];
                $sqlValues = ['?', '?', '?', '?', '?'];
                $executeParams = [$show->id, $vehicle->id, $vehicle->owner_id, $categoryId, 'approved'];

                if ($paymentStatusColumn) {
                    $sqlColumns[] = $paymentStatusColumn;
                    $sqlValues[] = '?';
                    $executeParams[] = 'completed';
                }

                if (in_array($feeColumn, $columnNames)) {
                    $sqlColumns[] = $feeColumn;
                    $sqlValues[] = '?';
                    $executeParams[] = 45.00;
                }

                if (in_array('registration_number', $columnNames)) {
                    $sqlColumns[] = 'registration_number';
                    $sqlValues[] = '?';
                    $executeParams[] = $registrationNumber;
                }

                if (in_array('display_number', $columnNames)) {
                    $sqlColumns[] = 'display_number';
                    $sqlValues[] = '?';
                    $executeParams[] = $displayNumber;
                }

                $sqlColumns[] = 'created_at';
                $sqlValues[] = 'NOW()';

                $sql = "INSERT INTO registrations (" . implode(', ', $sqlColumns) . ") VALUES (" . implode(', ', $sqlValues) . ")";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute($executeParams);

                $generated++;
            }

            if ($verbose && $generated % 10 == 0) {
                echo "  Generated {$generated} registrations...\n";
            }
        }

        $this->generatedData['Registrations'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} vehicle registrations\n";
    }

    /**
     * Generate payment records
     */
    private function generatePayments($verbose = true) {
        // First check the payments table structure
        $this->db->query("DESCRIBE payments");
        $paymentColumns = $this->db->resultSet();
        $paymentColumnNames = array_column($paymentColumns, 'Field');

        // Check if payment_method_id is required
        $requiresPaymentMethodId = in_array('payment_method_id', $paymentColumnNames);

        if ($requiresPaymentMethodId) {
            // Check if payment methods exist
            $this->db->query("SELECT COUNT(*) as count FROM payment_methods");
            $pmResult = $this->db->single();
            $paymentMethodCount = $pmResult->count ?? 0;

            if ($paymentMethodCount == 0) {
                // Create a default payment method
                $this->db->query("INSERT INTO payment_methods (name, description, is_active, created_at) VALUES ('Demo Payment Method', 'Demo payment method for testing', 1, NOW())");
                $this->db->execute();
                if ($verbose) echo "  📝 Created default payment method\n";
            }

            // Get the first payment method ID
            $this->db->query("SELECT id FROM payment_methods WHERE is_active = 1 LIMIT 1");
            $pmResult = $this->db->single();
            $defaultPaymentMethodId = $pmResult->id ?? 1;
        }

        // Get registrations with proper user ID handling
        $this->db->query("DESCRIBE registrations");
        $regColumns = $this->db->resultSet();
        $regColumnNames = array_column($regColumns, 'Field');

        // Determine the user column in registrations
        $userColumn = in_array('user_id', $regColumnNames) ? 'user_id' : 'owner_id';
        $feeColumn = in_array('fee', $regColumnNames) ? 'fee' : 'payment_amount';

        $this->db->query("SELECT r.id, r.{$userColumn} as user_id, r.{$feeColumn} as fee, s.name as show_name
                          FROM registrations r
                          JOIN shows s ON r.show_id = s.id
                          WHERE r.{$userColumn} IS NOT NULL
                          ORDER BY r.id");
        $registrations = $this->db->resultSet();

        if (empty($registrations)) {
            if ($verbose) echo "  ⚠️ No registrations found with valid user IDs\n";
            return;
        }

        $paymentMethods = ['Credit Card', 'PayPal', 'Bank Transfer', 'Cash'];
        $generated = 0;

        foreach ($registrations as $registration) {
            // Verify the user exists
            $stmt = $this->db->getConnection()->prepare("SELECT id FROM users WHERE id = ?");
            $stmt->execute([$registration->user_id]);
            $userExists = $stmt->fetch();

            if (!$userExists) {
                if ($verbose) echo "    ⚠️ Skipping payment for non-existent user ID: {$registration->user_id}\n";
                continue;
            }

            $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
            $reference = 'PAY-' . strtoupper(substr(md5($registration->id . time()), 0, 10));

            // Build SQL based on available columns
            $sqlColumns = ['user_id', 'amount', 'payment_status', 'payment_reference', 'payment_type', 'related_id', 'notes'];
            $sqlValues = ['?', '?', '?', '?', '?', '?', '?'];
            $executeParams = [
                $registration->user_id,
                $registration->fee ?? 45.00,
                'completed',
                $reference,
                'registration',
                $registration->id,
                "Registration payment for {$registration->show_name} - {$paymentMethod}"
            ];

            if ($requiresPaymentMethodId) {
                $sqlColumns[] = 'payment_method_id';
                $sqlValues[] = '?';
                $executeParams[] = $defaultPaymentMethodId;
            }

            $sqlColumns[] = 'created_at';
            $sqlValues[] = 'NOW()';

            $sql = "INSERT INTO payments (" . implode(', ', $sqlColumns) . ") VALUES (" . implode(', ', $sqlValues) . ")";

            try {
                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute($executeParams);
                $generated++;

                if ($verbose && $generated % 20 == 0) {
                    echo "    Generated {$generated} payments...\n";
                }
            } catch (Exception $e) {
                if ($verbose) {
                    echo "    ⚠️ Failed to create payment for registration {$registration->id}: " . $e->getMessage() . "\n";
                }
            }
        }

        $this->generatedData['Payments'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} payment records\n";
    }

    /**
     * Assign judges to categories for proper judging
     */
    private function assignJudgesToCategories($verbose = true) {
        try {
            $assignmentsCreated = 0;

            // Ensure judge_assignments table exists with proper structure
            $this->ensureJudgeAssignmentsTable();

            // Get all demo shows
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            foreach ($demoShowNames as $showName) {
                // Get show ID
                $stmt = $this->db->getConnection()->prepare("SELECT id FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $show = $stmt->fetch(PDO::FETCH_OBJ);

                if (!$show) continue;

                // Get judges, admins, and coordinators (all can judge)
                $this->db->query("SELECT id, name FROM users WHERE role IN ('judge', 'admin', 'coordinator') ORDER BY RAND() LIMIT 8");
                $judges = $this->db->resultSet();

                // Get categories for this show
                $this->db->query("SELECT id, name FROM show_categories WHERE show_id = :show_id");
                $this->db->bind(':show_id', $show->id);
                $categories = $this->db->resultSet();

                if (empty($judges)) {
                    if ($verbose) {
                        echo "    ⚠️ No judges found for show: {$showName}\n";
                    }
                    continue;
                }

                if (empty($categories)) {
                    if ($verbose) {
                        echo "    ⚠️ No categories found for show: {$showName}\n";
                    }
                    continue;
                }

                // Assign judges to specific categories
                foreach ($categories as $category) {
                    // Assign 2-3 judges per category
                    $numJudges = rand(2, min(3, count($judges)));
                    $judgesForCategory = array_slice($judges, 0, $numJudges);

                    foreach ($judgesForCategory as $judge) {
                        // Check if assignment already exists
                        $stmt = $this->db->getConnection()->prepare(
                            "SELECT id FROM judge_assignments WHERE show_id = ? AND judge_id = ? AND category_id = ?"
                        );
                        $stmt->execute([$show->id, $judge->id, $category->id]);

                        if (!$stmt->fetch()) {
                            // Create assignment with proper columns
                            $stmt = $this->db->getConnection()->prepare(
                                "INSERT INTO judge_assignments (show_id, judge_id, category_id, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())"
                            );
                            $stmt->execute([$show->id, $judge->id, $category->id]);
                            $assignmentsCreated++;

                            if ($verbose) {
                                echo "    ✅ Assigned judge {$judge->name} to category {$category->name} in {$showName}\n";
                            }
                        }
                    }
                }

                // Also assign some judges to judge all categories (category_id = NULL)
                $generalJudges = array_slice($judges, 0, min(2, count($judges)));
                foreach ($generalJudges as $judge) {
                    // Check if general assignment already exists
                    $stmt = $this->db->getConnection()->prepare(
                        "SELECT id FROM judge_assignments WHERE show_id = ? AND judge_id = ? AND category_id IS NULL"
                    );
                    $stmt->execute([$show->id, $judge->id]);

                    if (!$stmt->fetch()) {
                        // Create general assignment
                        $stmt = $this->db->getConnection()->prepare(
                            "INSERT INTO judge_assignments (show_id, judge_id, category_id, created_at, updated_at) VALUES (?, ?, NULL, NOW(), NOW())"
                        );
                        $stmt->execute([$show->id, $judge->id]);
                        $assignmentsCreated++;

                        if ($verbose) {
                            echo "    ✅ Assigned judge {$judge->name} as general judge for {$showName}\n";
                        }
                    }
                }
            }

            $this->generatedData['Judge Assignments'] = $assignmentsCreated;
            if ($verbose) echo "  ✅ Created {$assignmentsCreated} judge assignments\n";

        } catch (Exception $e) {
            if ($verbose) {
                echo "  ❌ Error creating judge assignments: " . $e->getMessage() . "\n";
            }
            error_log("Demo judge assignment error: " . $e->getMessage());
        }
    }

    /**
     * Generate judging scores using the actual scoring system tables
     */
    private function generateJudgingScores($verbose = true) {
        // Get all registrations for demo shows only
        $demoShowNames = [
            'Southern California Classic Car Showcase',
            'Miami Beach Exotic Car Festival',
            'Texas Muscle Car Madness',
            'Arizona Desert Classic Concours',
            'Atlanta Import Tuner Expo',
            'Rocky Mountain Vintage Rally',
            'Pacific Northwest Euro Fest',
            'Music City Hot Rod Nationals',
            'Charlotte Motor Speedway Car Show',
            'Las Vegas Strip Supercar Spectacular'
        ];

        $showNamePlaceholders = implode(',', array_fill(0, count($demoShowNames), '?'));

        // Get registrations for demo shows only
        $stmt = $this->db->getConnection()->prepare("
            SELECT r.id as registration_id, r.show_id, r.vehicle_id, r.category_id, s.name as show_name
            FROM registrations r
            JOIN shows s ON r.show_id = s.id
            WHERE s.name IN ({$showNamePlaceholders})
            ORDER BY r.id
        ");
        $stmt->execute($demoShowNames);
        $registrations = $stmt->fetchAll(PDO::FETCH_OBJ);

        if (empty($registrations)) {
            if ($verbose) echo "  ⚠️ No registrations found for demo shows\n";
            return;
        }

        // Create the scoring tables if they don't exist
        $this->createScoringTables($verbose);

        $comments = [
            'Exterior Condition' => [
                'Exceptional paint quality with deep, lustrous finish. Chrome is flawless.',
                'Outstanding restoration work. Paint depth and clarity are museum quality.',
                'Beautiful color choice with excellent execution. Minor stone chips noted.',
                'Impressive attention to detail. Panel gaps are perfect throughout.'
            ],
            'Interior Quality' => [
                'Original interior in remarkable condition. Seats show minimal wear.',
                'Beautiful restoration with period-correct materials and colors.',
                'Dashboard and trim pieces are in excellent condition.',
                'Upholstery work is professional grade. Attention to detail is evident.'
            ],
            'Engine Bay' => [
                'Engine bay is detailed to concours standards. Everything is correct.',
                'Impressive presentation with proper finishes and correct components.',
                'Clean, organized layout with attention to period-correct details.',
                'Engine appears to be numbers matching with proper date codes.'
            ],
            'Undercarriage' => [
                'Underside is detailed and clean. No rust or corrosion visible.',
                'Suspension components appear original and properly finished.',
                'Exhaust system is correct for the application.',
                'Frame and chassis show excellent restoration work.'
            ],
            'Authenticity' => [
                'All components appear period-correct and properly dated.',
                'Excellent research evident in component selection and finishes.',
                'Documentation supports authenticity claims.',
                'Rare options are properly represented and functional.'
            ],
            'Craftsmanship' => [
                'Restoration work is of the highest professional standard.',
                'Attention to detail is evident throughout the vehicle.',
                'Fit and finish exceed factory specifications.',
                'Workmanship demonstrates master-level skills.'
            ]
        ];

        $generated = 0;
        $judgeScoresGenerated = 0;
        $metricScoresGenerated = 0;

        foreach ($registrations as $registration) {
            // Get judging metrics for this show
            $this->db->query("SELECT id, name, max_score, weight FROM judging_metrics WHERE show_id = :show_id ORDER BY id");
            $this->db->bind(':show_id', $registration->show_id);
            $metrics = $this->db->resultSet();

            if (empty($metrics)) continue;

            // Get judges assigned to this show and category
            $stmt = $this->db->getConnection()->prepare("
                SELECT DISTINCT u.id, u.name
                FROM users u
                JOIN judge_assignments ja ON u.id = ja.judge_id
                WHERE ja.show_id = ?
                AND (ja.category_id = ? OR ja.category_id IS NULL)
                ORDER BY RAND()
            ");
            $stmt->execute([$registration->show_id, $registration->category_id]);
            $assignedJudges = $stmt->fetchAll(PDO::FETCH_OBJ);

            if (empty($assignedJudges)) {
                if ($verbose) {
                    echo "    ⚠️ No judges assigned to show {$registration->show_name} for registration {$registration->registration_id}\n";
                }
                continue;
            }

            foreach ($assignedJudges as $judge) {
                $totalScore = 0;
                $totalWeight = 0;
                $judgeMetricScores = [];

                // Calculate scores for each metric
                foreach ($metrics as $metric) {
                    // Generate realistic scores (70-100% of max score)
                    $minScore = $metric->max_score * 0.7;
                    $maxScore = $metric->max_score;
                    $rawScore = round(rand($minScore * 100, $maxScore * 100) / 100, 2);
                    $weightedScore = $rawScore * $metric->weight;

                    $totalScore += $weightedScore;
                    $totalWeight += $metric->weight;

                    // Get appropriate comment
                    $metricComments = $comments[$metric->name] ?? ['Excellent work overall.'];
                    $comment = $metricComments[array_rand($metricComments)];

                    $judgeMetricScores[] = [
                        'metric_id' => $metric->id,
                        'metric_name' => $metric->name,
                        'raw_score' => $rawScore,
                        'max_score' => $metric->max_score,
                        'weight' => $metric->weight,
                        'weighted_score' => $weightedScore,
                        'comments' => $comment
                    ];
                }

                // Calculate final score
                $finalScore = $totalWeight > 0 ? $totalScore / $totalWeight : 0;

                // Insert judge total score
                $sql = "INSERT INTO judge_total_scores (show_id, vehicle_id, registration_id, judge_id, judge_name,
                        raw_score, weighted_score, final_score, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $registration->show_id,
                    $registration->vehicle_id,
                    $registration->registration_id,
                    $judge->id,
                    'Demo Judge ' . $judge->id,
                    $totalScore,
                    $totalScore,
                    $finalScore
                ]);

                $judgeTotalScoreId = $this->db->getConnection()->lastInsertId();
                $judgeScoresGenerated++;

                // Insert metric scores
                foreach ($judgeMetricScores as $metricScore) {
                    $sql = "INSERT INTO judge_metric_scores (judge_total_score_id, show_id, vehicle_id, registration_id,
                            judge_id, metric_id, metric_name, raw_score, max_score, weight, weighted_score,
                            age_weighted_score, comments, created_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

                    $stmt = $this->db->getConnection()->prepare($sql);
                    $stmt->execute([
                        $judgeTotalScoreId,
                        $registration->show_id,
                        $registration->vehicle_id,
                        $registration->registration_id,
                        $judge->id,
                        $metricScore['metric_id'],
                        $metricScore['metric_name'],
                        $metricScore['raw_score'],
                        $metricScore['max_score'],
                        $metricScore['weight'],
                        $metricScore['weighted_score'],
                        $metricScore['weighted_score'], // Same as weighted for demo
                        $metricScore['comments']
                    ]);

                    $metricScoresGenerated++;
                }

                $generated++;
            }

            if ($verbose && $generated % 20 == 0) {
                echo "  Generated {$generated} judge score sets...\n";
            }
        }

        $this->generatedData['Judge Total Scores'] = $judgeScoresGenerated;
        $this->generatedData['Judge Metric Scores'] = $metricScoresGenerated;
        if ($verbose) echo "  ✅ Generated {$judgeScoresGenerated} judge total scores and {$metricScoresGenerated} metric scores\n";
    }

    /**
     * Generate fan votes
     */
    private function generateFanVotes($verbose = true) {
        // Get all registrations
        $this->db->query("SELECT id, show_id FROM registrations ORDER BY id");
        $registrations = $this->db->resultSet();

        $generated = 0;

        foreach ($registrations as $registration) {
            // Generate 5-25 fan votes per vehicle
            $voteCount = rand(5, 25);

            for ($i = 0; $i < $voteCount; $i++) {
                // Generate unique IP addresses
                $ip = rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);

                $sql = "INSERT IGNORE INTO fan_votes (show_id, registration_id, voter_ip, created_at)
                        VALUES (?, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $registration->show_id,
                    $registration->id,
                    $ip
                ]);

                $generated++;
            }

            if ($verbose && $generated % 100 == 0) {
                echo "  Generated {$generated} fan votes...\n";
            }
        }

        $this->generatedData['Fan Votes'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} fan votes\n";
    }

    /**
     * Create upload directories if they don't exist
     */
    private function createUploadDirectories() {
        $directories = [
            '../../uploads',
            '../../uploads/vehicles',
            '../../uploads/shows',
            '../../uploads/temp'
        ];

        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * Download image from URL and save to local storage
     *
     * @param string $url Image URL
     * @param string $filename Local filename
     * @param string $directory Upload directory
     * @return string|false Local file path or false on failure
     */
    private function downloadImage($url, $filename, $directory) {
        try {
            // Create directory if it doesn't exist
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            $localPath = $directory . '/' . $filename;

            // Try to download image with multiple fallback URLs
            $imageData = $this->downloadImageWithFallbacks($url);

            if ($imageData === false) {
                return false;
            }

            // Save image
            if (file_put_contents($localPath, $imageData) === false) {
                return false;
            }

            return $localPath;

        } catch (Exception $e) {
            error_log("Error downloading image from {$url}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate and download images for vehicles and shows using ImageEditorModel
     */
    private function generateImages($verbose = true) {
        $vehicleImages = 0;
        $showImages = 0;
        $bannerImages = 0;

        try {
            // Load the ImageEditorModel
            require_once '../../models/ImageEditorModel.php';
            $imageEditorModel = new ImageEditorModel();

            // Ensure banner_image column exists in shows table
            $this->ensureBannerImageColumn();

            // Generate vehicle images
            if ($verbose) echo "  📱 Downloading vehicle images...\n";

            $this->db->query("SELECT id FROM vehicles WHERE vin REGEXP '^DEMO[0-9]{13}$' ORDER BY id");
            $vehicles = $this->db->resultSet();

            foreach ($vehicles as $index => $vehicle) {
                if ($index >= count($this->vehicleImageUrls)) break;

                $imageUrl = $this->vehicleImageUrls[$index];
                $filename = 'demo_vehicle_' . $vehicle->id . '_' . time() . '.jpg';

                // Download image to temporary location
                $tempPath = $this->downloadImageToTemp($imageUrl, $filename);

                if ($tempPath) {
                    // Create a fake $_FILES array for the ImageEditorModel
                    $fakeFile = [
                        'name' => $filename,
                        'type' => 'image/jpeg',
                        'tmp_name' => $tempPath,
                        'error' => UPLOAD_ERR_OK,
                        'size' => filesize($tempPath)
                    ];

                    // Use ImageEditorModel to process the upload properly
                    $result = $imageEditorModel->processImageUpload(
                        $fakeFile,
                        'vehicle',
                        $vehicle->id,
                        'uploads/vehicles/',
                        1, // Admin user ID
                        true // Set as primary
                    );

                    if ($result) {
                        $vehicleImages++;

                        if ($verbose && $vehicleImages % 5 == 0) {
                            echo "    Downloaded {$vehicleImages} vehicle images...\n";
                        }
                    } else {
                        if ($verbose) {
                            echo "    ℹ️ Failed to process image for vehicle {$vehicle->id}\n";
                        }
                    }

                    // Clean up temp file
                    if (file_exists($tempPath)) {
                        unlink($tempPath);
                    }
                } else {
                    if ($verbose) {
                        echo "    ℹ️ Skipped image for vehicle {$vehicle->id} (all sources unavailable)\n";
                    }
                }

                // Small delay to be respectful to image servers
                usleep(500000); // 0.5 seconds
            }

            // Generate show images
            if ($verbose) echo "  🏆 Downloading show images...\n";

            // Get demo shows by name
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            foreach ($demoShowNames as $index => $showName) {
                if ($index >= count($this->showImageUrls)) break;

                // Get show ID
                $stmt = $this->db->getConnection()->prepare("SELECT id FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $show = $stmt->fetch(PDO::FETCH_OBJ);

                if (!$show) continue;

                $imageUrl = $this->showImageUrls[$index];
                $filename = 'demo_show_' . $show->id . '_' . time() . '.jpg';

                // Download image to temporary location
                $tempPath = $this->downloadImageToTemp($imageUrl, $filename);

                if ($tempPath) {
                    // Create a fake $_FILES array for the ImageEditorModel
                    $fakeFile = [
                        'name' => $filename,
                        'type' => 'image/jpeg',
                        'tmp_name' => $tempPath,
                        'error' => UPLOAD_ERR_OK,
                        'size' => filesize($tempPath)
                    ];

                    // Use ImageEditorModel to process the upload properly
                    $result = $imageEditorModel->processImageUpload(
                        $fakeFile,
                        'show',
                        $show->id,
                        'uploads/shows/',
                        1, // Admin user ID
                        true // Set as primary
                    );

                    if ($result) {
                        $showImages++;

                        // Get the actual filename stored by ImageEditorModel
                        $actualFilename = $this->getImageFilename('show', $show->id);
                        if ($actualFilename) {
                            // Set this image as the banner image for the show
                            $this->setShowBannerImage($show->id, $actualFilename);
                            $bannerImages++;
                        }

                        if ($verbose && $showImages % 3 == 0) {
                            echo "    Downloaded {$showImages} show images...\n";
                        }
                    } else {
                        if ($verbose) {
                            echo "    ℹ️ Failed to process image for show {$show->id}\n";
                        }
                    }

                    // Clean up temp file
                    if (file_exists($tempPath)) {
                        unlink($tempPath);
                    }
                } else {
                    if ($verbose) {
                        echo "    ℹ️ Skipped image for show {$show->id} (all sources unavailable)\n";
                    }
                }

                // Small delay to be respectful to image servers
                usleep(500000); // 0.5 seconds
            }

            $this->generatedData['Vehicle Images'] = $vehicleImages;
            $this->generatedData['Show Images'] = $showImages;
            $this->generatedData['Banner Images'] = $bannerImages;

            if ($verbose) {
                echo "  ✅ Downloaded {$vehicleImages} vehicle images, {$showImages} show images, and set {$bannerImages} banner images\n";
            }

        } catch (Exception $e) {
            if ($verbose) {
                echo "  ❌ Error downloading images: " . $e->getMessage() . "\n";
            }
            error_log("Demo image generation error: " . $e->getMessage());
        }
    }

    /**
     * Helper method to clean up table data
     */
    private function cleanupTableData($tableName, $conditions = [], $verbose = false) {
        try {
            // Check if table exists
            $this->db->query("SHOW TABLES LIKE '{$tableName}'");
            if ($this->db->rowCount() == 0) {
                return 0; // Table doesn't exist
            }

            if (empty($conditions)) {
                // Delete all records from table (for tables like fan_votes, judging_scores that don't have user references)
                $this->db->query("DELETE FROM {$tableName}");
                $deleted = $this->db->execute() ? $this->db->rowCount() : 0;
            } else {
                // Build WHERE clause for conditions
                $whereParts = [];
                $params = [];

                foreach ($conditions as $column => $values) {
                    if (is_array($values) && !empty($values)) {
                        $placeholders = str_repeat('?,', count($values) - 1) . '?';
                        $whereParts[] = "{$column} IN ({$placeholders})";
                        $params = array_merge($params, $values);
                    } elseif (!is_array($values)) {
                        $whereParts[] = "{$column} = ?";
                        $params[] = $values;
                    }
                }

                if (!empty($whereParts)) {
                    $whereClause = implode(' AND ', $whereParts);
                    $sql = "DELETE FROM {$tableName} WHERE {$whereClause}";

                    $stmt = $this->db->getConnection()->prepare($sql);
                    $stmt->execute($params);
                    $deleted = $stmt->rowCount();
                } else {
                    $deleted = 0;
                }
            }

            if ($verbose && $deleted > 0) {
                echo "    ✅ Deleted {$deleted} records from {$tableName}\n";
            }

            return $deleted;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning {$tableName}: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Clean up demo images and files (TARGETED - only demo vehicle/show images)
     * Uses ImageEditorModel for complete cleanup including thumbnails
     */
    private function cleanupDemoImages($demoUserIds, $verbose = false) {
        try {
            // Load the ImageEditorModel for proper deletion
            require_once '../../models/ImageEditorModel.php';
            $imageEditorModel = new ImageEditorModel();

            $deletedImages = 0;
            $imagesToDelete = [];

            // Method 1: Get images linked to demo vehicles
            $demoVehicleImages = $this->getDemoVehicleImages($verbose);
            $imagesToDelete = array_merge($imagesToDelete, $demoVehicleImages);

            // Method 2: Get images linked to demo shows
            $demoShowImages = $this->getDemoShowImages($verbose);
            $imagesToDelete = array_merge($imagesToDelete, $demoShowImages);

            // Method 3: Get images with exact demo filename patterns (very specific)
            $demoPatternImages = $this->getDemoPatternImages($verbose);
            $imagesToDelete = array_merge($imagesToDelete, $demoPatternImages);

            // Remove duplicates
            $uniqueImages = [];
            foreach ($imagesToDelete as $image) {
                $uniqueImages[$image->id] = $image;
            }

            if ($verbose && !empty($uniqueImages)) {
                echo "    📊 Found " . count($uniqueImages) . " demo images to delete\n";
            }

            foreach ($uniqueImages as $image) {
                // Use ImageEditorModel for complete deletion (includes thumbnails, optimized files, etc.)
                $result = $imageEditorModel->deleteImage($image->id);

                if ($result) {
                    $deletedImages++;

                    if ($verbose) {
                        echo "    🗑️ Deleted image: {$image->file_name} (ID: {$image->id})\n";
                    }
                } else {
                    if ($verbose) {
                        echo "    ⚠️ Failed to delete image: {$image->file_name} (ID: {$image->id})\n";
                    }
                }
            }

            if ($verbose && $deletedImages > 0) {
                echo "    ✅ Deleted {$deletedImages} demo images (including thumbnails and database records)\n";
            } elseif ($verbose) {
                echo "    ℹ️ No demo images found to delete\n";
            }

            return $deletedImages;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning up images: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Get images linked to demo vehicles only
     */
    private function getDemoVehicleImages($verbose = false) {
        try {
            // Get demo vehicles (with exact DEMO VIN pattern)
            $this->db->query("SELECT id FROM vehicles WHERE vin REGEXP '^DEMO[0-9]{13}$' AND license_plate REGEXP '^DEMO[0-9]{3}$'");
            $demoVehicles = $this->db->resultSet();

            if (empty($demoVehicles)) {
                return [];
            }

            $demoVehicleIds = array_column($demoVehicles, 'id');
            $placeholders = str_repeat('?,', count($demoVehicleIds) - 1) . '?';

            // Get images linked to these demo vehicles
            $stmt = $this->db->getConnection()->prepare("SELECT * FROM images WHERE vehicle_id IN ({$placeholders})");
            $stmt->execute($demoVehicleIds);
            $images = $stmt->fetchAll(PDO::FETCH_OBJ);

            if ($verbose && !empty($images)) {
                echo "    🚗 Found " . count($images) . " images linked to demo vehicles\n";
            }

            return $images;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error getting demo vehicle images: " . $e->getMessage() . "\n";
            }
            return [];
        }
    }

    /**
     * Get images linked to demo shows only
     */
    private function getDemoShowImages($verbose = false) {
        try {
            // Get demo show IDs
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            $demoShowIds = [];
            foreach ($demoShowNames as $showName) {
                $stmt = $this->db->getConnection()->prepare("SELECT id FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $result = $stmt->fetch(PDO::FETCH_OBJ);
                if ($result) {
                    $demoShowIds[] = $result->id;
                }
            }

            if (empty($demoShowIds)) {
                return [];
            }

            $placeholders = str_repeat('?,', count($demoShowIds) - 1) . '?';

            // Get images linked to these demo shows
            $stmt = $this->db->getConnection()->prepare("SELECT * FROM images WHERE show_id IN ({$placeholders})");
            $stmt->execute($demoShowIds);
            $images = $stmt->fetchAll(PDO::FETCH_OBJ);

            if ($verbose && !empty($images)) {
                echo "    🏆 Found " . count($images) . " images linked to demo shows\n";
            }

            return $images;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error getting demo show images: " . $e->getMessage() . "\n";
            }
            return [];
        }
    }

    /**
     * Get images with very specific demo patterns only
     */
    private function getDemoPatternImages($verbose = false) {
        try {
            // Only get images with very specific demo patterns
            $patterns = [
                'demo_vehicle_%',
                'demo_show_%',
                'demo_car_%',
                'demo_event_%'
            ];

            $images = [];
            foreach ($patterns as $pattern) {
                $stmt = $this->db->getConnection()->prepare("SELECT * FROM images WHERE file_name LIKE ?");
                $stmt->execute([$pattern]);
                $patternImages = $stmt->fetchAll(PDO::FETCH_OBJ);
                $images = array_merge($images, $patternImages);
            }

            if ($verbose && !empty($images)) {
                echo "    📁 Found " . count($images) . " images with demo filename patterns\n";
            }

            return $images;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error getting demo pattern images: " . $e->getMessage() . "\n";
            }
            return [];
        }
    }

    /**
     * Clean up orphaned demo data by pattern matching
     */
    private function cleanupOrphanedDemoData($verbose = false) {
        try {
            $orphanedCount = 0;

            // Clean up any remaining demo files in upload directories
            $uploadDirs = ['../../uploads/vehicles', '../../uploads/shows'];

            foreach ($uploadDirs as $dir) {
                if (is_dir($dir)) {
                    $files = glob($dir . '/demo_*');
                    foreach ($files as $file) {
                        if (is_file($file)) {
                            unlink($file);
                            $orphanedCount++;
                        }
                    }
                }
            }

            // CONSERVATIVE CLEANUP - Only remove data that is clearly demo-related

            // Clean up registrations with EXACT demo registration number pattern (RER-000001 to RER-999999)
            $this->db->query("DELETE FROM registrations WHERE registration_number REGEXP '^RER-[0-9]{6}$'");
            $deleted = $this->db->execute() ? $this->db->rowCount() : 0;
            $orphanedCount += $deleted;
            if ($verbose && $deleted > 0) {
                echo "    ✅ Deleted {$deleted} demo registrations (exact pattern RER-######)\n";
            }

            // Payment cleanup is now handled by cleanupDemoPayments() method

            // Clean up vehicles with EXACT demo patterns only
            $this->db->query("DELETE FROM vehicles WHERE (vin REGEXP '^DEMO[0-9]{13}$' AND license_plate REGEXP '^DEMO[0-9]{3}$')");
            $deleted = $this->db->execute() ? $this->db->rowCount() : 0;
            $orphanedCount += $deleted;
            if ($verbose && $deleted > 0) {
                echo "    ✅ Deleted {$deleted} demo vehicles (exact DEMO patterns only)\n";
            }

            // Only clean up categories/metrics for shows that were already deleted in this session
            // (Don't touch existing show data)
            if ($verbose) {
                echo "    ℹ️ Skipping orphaned cleanup to protect existing show data\n";
            }

            if ($verbose && $orphanedCount > 0) {
                echo "    🎯 Total orphaned records/files cleaned: {$orphanedCount}\n";
            }

            return $orphanedCount;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning orphaned data: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Clean up demo shows with comprehensive pattern matching
     */
    private function cleanupDemoShows($demoUserIds, $verbose = false) {
        try {
            $deletedShows = 0;

            // First, clear banner images for demo shows
            $this->clearShowBannerImages($verbose);

            // Method 1: Delete shows with demo coordinator IDs
            if (!empty($demoUserIds)) {
                $placeholders = str_repeat('?,', count($demoUserIds) - 1) . '?';
                $sql = "DELETE FROM shows WHERE coordinator_id IN ({$placeholders})";
                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute($demoUserIds);
                $deletedShows += $stmt->rowCount();

                if ($verbose && $stmt->rowCount() > 0) {
                    echo "    ✅ Deleted {$stmt->rowCount()} shows with demo coordinators\n";
                }
            }

            // Method 2: Delete shows by name patterns (our demo show names)
            $demoShowPatterns = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            foreach ($demoShowPatterns as $showName) {
                $this->db->query("DELETE FROM shows WHERE name = ?");
                $stmt = $this->db->getConnection()->prepare("DELETE FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $deleted = $stmt->rowCount();
                $deletedShows += $deleted;

                if ($verbose && $deleted > 0) {
                    echo "    ✅ Deleted demo show: '{$showName}'\n";
                }
            }

            // Method 3: Delete shows with demo-like descriptions (containing our specific text)
            $demoDescriptionPatterns = [
                '%Join us for the premier classic car event%',
                '%Experience the ultimate luxury car event%',
                '%Rev up for the biggest muscle car event%',
                '%An elegant concours d\'elegance%',
                '%The Southeast\'s premier import and tuner%',
                '%A scenic mountain rally featuring%',
                '%Celebrating European automotive excellence%',
                '%Nashville\'s hottest rod and custom%',
                '%Experience automotive excellence at the legendary%',
                '%The most glamorous supercar event%'
            ];

            foreach ($demoDescriptionPatterns as $pattern) {
                $this->db->query("DELETE FROM shows WHERE description LIKE ?");
                $stmt = $this->db->getConnection()->prepare("DELETE FROM shows WHERE description LIKE ?");
                $stmt->execute([$pattern]);
                $deleted = $stmt->rowCount();
                $deletedShows += $deleted;

                if ($verbose && $deleted > 0) {
                    echo "    ✅ Deleted {$deleted} shows matching description pattern\n";
                }
            }

            // Method 4: Only delete shows created very recently (within last 10 minutes) with demo names AND invalid coordinators
            $demoNameConditions = array_map(function($name) {
                return "name = " . $this->db->getConnection()->quote($name);
            }, $demoShowPatterns);
            $demoNameClause = "(" . implode(' OR ', $demoNameConditions) . ")";

            $this->db->query("DELETE FROM shows WHERE created_at > DATE_SUB(NOW(), INTERVAL 10 MINUTE) AND ({$demoNameClause}) AND coordinator_id NOT IN (SELECT id FROM users)");
            $deleted = $this->db->execute() ? $this->db->rowCount() : 0;
            $deletedShows += $deleted;

            if ($verbose && $deleted > 0) {
                echo "    ✅ Deleted {$deleted} recent demo shows with invalid coordinators\n";
            }

            // Method 5: Delete shows with non-existent coordinator IDs (orphaned)
            $this->db->query("DELETE FROM shows WHERE coordinator_id IS NOT NULL AND coordinator_id NOT IN (SELECT id FROM users)");
            $deleted = $this->db->execute() ? $this->db->rowCount() : 0;
            $deletedShows += $deleted;

            if ($verbose && $deleted > 0) {
                echo "    ✅ Deleted {$deleted} shows with invalid coordinator IDs\n";
            }

            if ($verbose && $deletedShows > 0) {
                echo "    🎯 Total shows deleted: {$deletedShows}\n";
            }

            return $deletedShows;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning up shows: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * SAFE cleanup function - only removes data with very specific demo patterns
     */
    public function safeCleanupDemoData($verbose = true) {
        if ($verbose) {
            echo "🛡️ SAFE Demo Data Cleanup (Conservative Mode)\n";
            echo "=============================================\n";
            echo "This cleanup only removes data with very specific demo patterns.\n\n";
        }

        $deletedCounts = [];

        try {
            // Only remove users with our specific test domains
            $domainConditions = array_map(function($domain) {
                return "email LIKE '%@{$domain}'";
            }, $this->testDomains);
            $whereClause = "(" . implode(' OR ', $domainConditions) . ")";

            // Get demo user IDs
            $this->db->query("SELECT id FROM users WHERE " . $whereClause);
            $demoUsers = $this->db->resultSet();
            $demoUserIds = array_column($demoUsers, 'id');

            if ($verbose) {
                echo "  👥 Found " . count($demoUserIds) . " users with test domains\n";
            }

            // Only remove shows with EXACT demo names
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            $deletedShows = 0;
            foreach ($demoShowNames as $showName) {
                $stmt = $this->db->getConnection()->prepare("DELETE FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $deleted = $stmt->rowCount();
                $deletedShows += $deleted;

                if ($verbose && $deleted > 0) {
                    echo "  🏆 Deleted demo show: '{$showName}'\n";
                }
            }
            $deletedCounts['shows'] = $deletedShows;

            // Only remove vehicles with EXACT demo VIN pattern (DEMO + 13 digits)
            $this->db->query("DELETE FROM vehicles WHERE vin REGEXP '^DEMO[0-9]{13}$' AND license_plate REGEXP '^DEMO[0-9]{3}$'");
            $deletedCounts['vehicles'] = $this->db->execute() ? $this->db->rowCount() : 0;

            // Only remove registrations with EXACT demo pattern (RER-000001 format)
            $this->db->query("DELETE FROM registrations WHERE registration_number REGEXP '^RER-[0-9]{6}$'");
            $deletedCounts['registrations'] = $this->db->execute() ? $this->db->rowCount() : 0;

            // Clean up demo payments (targeted)
            $deletedCounts['payments'] = $this->cleanupDemoPayments($verbose);

            // Clean up judge assignments and scores
            if ($verbose) echo "  👨‍⚖️ Cleaning up judge assignments and scores...\n";
            $judgingCounts = $this->cleanupJudgingData($verbose);
            $deletedCounts = array_merge($deletedCounts, $judgingCounts);

            // Only remove demo users
            if (!empty($demoUserIds)) {
                $this->db->query("DELETE FROM users WHERE " . $whereClause);
                $deletedCounts['users'] = $this->db->execute() ? $this->db->rowCount() : 0;
            }

            // Clean up demo images (targeted)
            if ($verbose) echo "  🖼️ Cleaning up demo images...\n";
            $deletedCounts['images'] = $this->cleanupDemoImages($demoUserIds, $verbose);

            // Clean up scoring tables (only demo data)
            $scoringCounts = $this->cleanupDemoScoringData($demoUserIds, $verbose);
            $deletedCounts = array_merge($deletedCounts, $scoringCounts);

            // Clean up fan votes (only demo shows)
            $deletedCounts['fan_votes'] = $this->cleanupDemoFanVotes($verbose);

            if ($verbose) {
                echo "\n✅ Safe Cleanup Complete!\n";
                echo "=========================\n";
                foreach ($deletedCounts as $table => $count) {
                    if ($count > 0) {
                        echo "  - {$table}: {$count} records deleted\n";
                    }
                }

                $totalDeleted = array_sum($deletedCounts);
                echo "\n🎯 Total: {$totalDeleted} demo records removed safely\n";
                echo "\n⚠️ If you still see leftover demo data, please let me know the specific items.\n";
            }

            return array_sum($deletedCounts);

        } catch (Exception $e) {
            if ($verbose) {
                echo "  ❌ Error during safe cleanup: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Create scoring tables if they don't exist
     */
    private function createScoringTables($verbose = false) {
        try {
            // Create judge_total_scores table
            $sql = "CREATE TABLE IF NOT EXISTS `judge_total_scores` (
                `id` int NOT NULL AUTO_INCREMENT,
                `show_id` int NOT NULL,
                `vehicle_id` int NOT NULL,
                `registration_id` int NOT NULL,
                `judge_id` int NOT NULL,
                `judge_name` varchar(255) DEFAULT NULL,
                `raw_score` decimal(10,2) DEFAULT NULL,
                `weighted_score` decimal(10,2) DEFAULT NULL,
                `age_weighted_score` decimal(10,2) DEFAULT NULL,
                `normalized_score` decimal(10,2) DEFAULT NULL,
                `final_score` decimal(10,2) DEFAULT NULL,
                `age_weight` decimal(10,4) DEFAULT '1.0000',
                `weight_multiplier` decimal(10,2) DEFAULT '100.00',
                `formula_id` int DEFAULT NULL,
                `formula_name` varchar(255) DEFAULT NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `unique_vehicle_judge` (`show_id`,`vehicle_id`,`judge_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            $this->db->query($sql);
            $this->db->execute();

            // Create judge_metric_scores table
            $sql = "CREATE TABLE IF NOT EXISTS `judge_metric_scores` (
                `id` int NOT NULL AUTO_INCREMENT,
                `judge_total_score_id` int NOT NULL,
                `show_id` int NOT NULL,
                `vehicle_id` int NOT NULL,
                `registration_id` int NOT NULL,
                `judge_id` int NOT NULL,
                `metric_id` int NOT NULL,
                `metric_name` varchar(255) DEFAULT NULL,
                `raw_score` decimal(10,2) DEFAULT NULL,
                `max_score` decimal(10,2) DEFAULT NULL,
                `weight` decimal(10,4) DEFAULT NULL,
                `weighted_score` decimal(10,2) DEFAULT NULL,
                `age_weighted_score` decimal(10,2) DEFAULT NULL,
                `comments` text,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            $this->db->query($sql);
            $this->db->execute();

            if ($verbose) echo "  📝 Created scoring tables\n";

        } catch (Exception $e) {
            if ($verbose) echo "  ⚠️ Error creating scoring tables: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Create fan_votes table if it doesn't exist
     */
    private function createFanVotesTable($verbose = false) {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS `fan_votes` (
                `id` int NOT NULL AUTO_INCREMENT,
                `show_id` int NOT NULL,
                `registration_id` int NOT NULL,
                `voter_ip` varchar(45) NOT NULL,
                `fb_user_id` varchar(100) NULL,
                `fb_user_name` varchar(255) NULL,
                `fb_user_email` varchar(255) NULL,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `show_ip` (`show_id`,`voter_ip`),
                KEY `registration_id` (`registration_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

            $this->db->query($sql);
            $this->db->execute();

            if ($verbose) echo "  📝 Created fan_votes table\n";

        } catch (Exception $e) {
            if ($verbose) echo "  ⚠️ Error creating fan_votes table: " . $e->getMessage() . "\n";
        }
    }

    /**
     * Clean up demo scoring data only (targeted cleanup)
     */
    private function cleanupDemoScoringData($demoUserIds, $verbose = false) {
        $deletedCounts = [];

        try {
            // Get demo show IDs
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            $demoShowIds = [];
            foreach ($demoShowNames as $showName) {
                $stmt = $this->db->getConnection()->prepare("SELECT id FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $result = $stmt->fetch(PDO::FETCH_OBJ);
                if ($result) {
                    $demoShowIds[] = $result->id;
                }
            }

            // Also get shows with demo coordinators
            if (!empty($demoUserIds)) {
                $placeholders = str_repeat('?,', count($demoUserIds) - 1) . '?';
                $stmt = $this->db->getConnection()->prepare("SELECT id FROM shows WHERE coordinator_id IN ({$placeholders})");
                $stmt->execute($demoUserIds);
                $coordinatorShows = $stmt->fetchAll(PDO::FETCH_OBJ);
                foreach ($coordinatorShows as $show) {
                    $demoShowIds[] = $show->id;
                }
            }

            $demoShowIds = array_unique($demoShowIds);

            if (empty($demoShowIds)) {
                if ($verbose) echo "    ℹ️ No demo shows found for scoring cleanup\n";
                return $deletedCounts;
            }

            $showPlaceholders = str_repeat('?,', count($demoShowIds) - 1) . '?';

            // Clean up judge_metric_scores for demo shows
            $stmt = $this->db->getConnection()->prepare("DELETE FROM judge_metric_scores WHERE show_id IN ({$showPlaceholders})");
            $stmt->execute($demoShowIds);
            $deletedCounts['judge_metric_scores'] = $stmt->rowCount();

            // Clean up judge_total_scores for demo shows
            $stmt = $this->db->getConnection()->prepare("DELETE FROM judge_total_scores WHERE show_id IN ({$showPlaceholders})");
            $stmt->execute($demoShowIds);
            $deletedCounts['judge_total_scores'] = $stmt->rowCount();

            // Clean up vehicle_metric_scores for demo shows
            $stmt = $this->db->getConnection()->prepare("DELETE FROM vehicle_metric_scores WHERE show_id IN ({$showPlaceholders})");
            $stmt->execute($demoShowIds);
            $deletedCounts['vehicle_metric_scores'] = $stmt->rowCount();

            // Clean up vehicle_total_scores for demo shows
            $stmt = $this->db->getConnection()->prepare("DELETE FROM vehicle_total_scores WHERE show_id IN ({$showPlaceholders})");
            $stmt->execute($demoShowIds);
            $deletedCounts['vehicle_total_scores'] = $stmt->rowCount();

            // Also clean up scores by demo judge IDs
            if (!empty($demoUserIds)) {
                $judgePlaceholders = str_repeat('?,', count($demoUserIds) - 1) . '?';

                $stmt = $this->db->getConnection()->prepare("DELETE FROM judge_metric_scores WHERE judge_id IN ({$judgePlaceholders})");
                $stmt->execute($demoUserIds);
                $deletedCounts['judge_metric_scores'] += $stmt->rowCount();

                $stmt = $this->db->getConnection()->prepare("DELETE FROM judge_total_scores WHERE judge_id IN ({$judgePlaceholders})");
                $stmt->execute($demoUserIds);
                $deletedCounts['judge_total_scores'] += $stmt->rowCount();
            }

            if ($verbose) {
                foreach ($deletedCounts as $table => $count) {
                    if ($count > 0) {
                        echo "    ✅ Deleted {$count} records from {$table}\n";
                    }
                }
            }

            return $deletedCounts;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning scoring data: " . $e->getMessage() . "\n";
            }
            return $deletedCounts;
        }
    }

    /**
     * Clean up demo fan votes only (targeted cleanup)
     */
    private function cleanupDemoFanVotes($verbose = false) {
        try {
            // Get demo show IDs
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            $demoShowIds = [];
            foreach ($demoShowNames as $showName) {
                $stmt = $this->db->getConnection()->prepare("SELECT id FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $result = $stmt->fetch(PDO::FETCH_OBJ);
                if ($result) {
                    $demoShowIds[] = $result->id;
                }
            }

            if (empty($demoShowIds)) {
                if ($verbose) echo "    ℹ️ No demo shows found for fan vote cleanup\n";
                return 0;
            }

            $placeholders = str_repeat('?,', count($demoShowIds) - 1) . '?';
            $stmt = $this->db->getConnection()->prepare("DELETE FROM fan_votes WHERE show_id IN ({$placeholders})");
            $stmt->execute($demoShowIds);
            $deleted = $stmt->rowCount();

            if ($verbose && $deleted > 0) {
                echo "    ✅ Deleted {$deleted} fan votes from demo shows\n";
            }

            return $deleted;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning fan votes: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Clean up demo payments only (highly targeted to protect real payments)
     */
    private function cleanupDemoPayments($verbose = false) {
        try {
            $deletedPayments = 0;

            // Method 1: Delete payments with exact demo payment reference pattern AND demo notes
            $this->db->query("DELETE FROM payments WHERE payment_reference REGEXP '^PAY-[A-Z0-9]{10}$' AND notes LIKE '%Registration payment for%'");
            $deleted1 = $this->db->execute() ? $this->db->rowCount() : 0;
            $deletedPayments += $deleted1;

            if ($verbose && $deleted1 > 0) {
                echo "    ✅ Deleted {$deleted1} payments with demo reference pattern\n";
            }

            // Method 2: Delete payments linked to demo registrations (by registration number pattern)
            $this->db->query("DELETE p FROM payments p
                              JOIN registrations r ON p.related_id = r.id
                              WHERE p.payment_type = 'registration'
                              AND r.registration_number REGEXP '^RER-[0-9]{6}$'");
            $deleted2 = $this->db->execute() ? $this->db->rowCount() : 0;
            $deletedPayments += $deleted2;

            if ($verbose && $deleted2 > 0) {
                echo "    ✅ Deleted {$deleted2} payments linked to demo registrations\n";
            }

            // Method 3: Delete payments for demo shows (by show name)
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            foreach ($demoShowNames as $showName) {
                $this->db->query("DELETE p FROM payments p
                                  JOIN registrations r ON p.related_id = r.id
                                  JOIN shows s ON r.show_id = s.id
                                  WHERE p.payment_type = 'registration'
                                  AND s.name = ?");
                $stmt = $this->db->getConnection()->prepare("DELETE p FROM payments p
                                                             JOIN registrations r ON p.related_id = r.id
                                                             JOIN shows s ON r.show_id = s.id
                                                             WHERE p.payment_type = 'registration'
                                                             AND s.name = ?");
                $stmt->execute([$showName]);
                $deleted3 = $stmt->rowCount();
                $deletedPayments += $deleted3;

                if ($verbose && $deleted3 > 0) {
                    echo "    ✅ Deleted {$deleted3} payments for demo show: {$showName}\n";
                }
            }

            // Method 4: Delete payments with demo notes patterns (very specific)
            $demoNotePatterns = [
                '%Demo payment method for testing%',
                '%Registration payment for Southern California Classic Car Showcase%',
                '%Registration payment for Miami Beach Exotic Car Festival%',
                '%Registration payment for Texas Muscle Car Madness%',
                '%Registration payment for Arizona Desert Classic Concours%',
                '%Registration payment for Atlanta Import Tuner Expo%',
                '%Registration payment for Rocky Mountain Vintage Rally%',
                '%Registration payment for Pacific Northwest Euro Fest%',
                '%Registration payment for Music City Hot Rod Nationals%',
                '%Registration payment for Charlotte Motor Speedway Car Show%',
                '%Registration payment for Las Vegas Strip Supercar Spectacular%'
            ];

            foreach ($demoNotePatterns as $pattern) {
                $stmt = $this->db->getConnection()->prepare("DELETE FROM payments WHERE notes LIKE ?");
                $stmt->execute([$pattern]);
                $deleted4 = $stmt->rowCount();
                $deletedPayments += $deleted4;

                if ($verbose && $deleted4 > 0) {
                    echo "    ✅ Deleted {$deleted4} payments with demo note pattern\n";
                }
            }

            if ($verbose && $deletedPayments > 0) {
                echo "    🎯 Total demo payments deleted: {$deletedPayments}\n";
            } elseif ($verbose) {
                echo "    ℹ️ No demo payments found to delete\n";
            }

            return $deletedPayments;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning demo payments: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Download image with fallback URLs and better error handling
     */
    private function downloadImageWithFallbacks($primaryUrl) {
        // Create fallback URLs for different image sources
        $fallbackUrls = [
            $primaryUrl,
            // Picsum fallbacks (reliable placeholder service)
            'https://picsum.photos/800/600?random=' . rand(1, 1000),
            'https://picsum.photos/800/600?random=' . rand(1001, 2000),
            'https://picsum.photos/800/600?random=' . rand(2001, 3000),
            // Lorem Picsum with different seeds
            'https://picsum.photos/seed/' . rand(1, 1000) . '/800/600',
            'https://picsum.photos/seed/' . rand(1001, 2000) . '/800/600'
        ];

        // If it's an Unsplash URL that failed, add some Unsplash fallbacks
        if (strpos($primaryUrl, 'unsplash.com') !== false) {
            $unsplashFallbacks = [
                'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1525609004556-c46c7d6cf023?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1542362567-b07e54358753?w=800&h=600&fit=crop',
                'https://images.unsplash.com/photo-1514316454349-750a7fd3da3a?w=800&h=600&fit=crop'
            ];

            // Insert Unsplash fallbacks after the primary URL
            array_splice($fallbackUrls, 1, 0, $unsplashFallbacks);
        }

        foreach ($fallbackUrls as $url) {
            try {
                // Suppress warnings and use error handling
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 15,
                        'user_agent' => 'Mozilla/5.0 (compatible; DemoDataGenerator/1.0)',
                        'ignore_errors' => true
                    ]
                ]);

                // Suppress warnings with @
                $imageData = @file_get_contents($url, false, $context);

                // Check if we got valid image data
                if ($imageData !== false && strlen($imageData) > 1000) {
                    // Verify it's actually an image by checking headers
                    $finfo = new finfo(FILEINFO_MIME_TYPE);
                    $mimeType = $finfo->buffer($imageData);

                    if (strpos($mimeType, 'image/') === 0) {
                        return $imageData;
                    }
                }

            } catch (Exception $e) {
                // Continue to next fallback
                continue;
            }
        }

        // If all fallbacks failed, create a simple colored rectangle as last resort
        return $this->createFallbackImage();
    }

    /**
     * Create a simple fallback image when all downloads fail
     */
    private function createFallbackImage() {
        try {
            // Create a simple 800x600 colored rectangle
            $width = 800;
            $height = 600;
            $image = imagecreate($width, $height);

            // Random colors for variety
            $colors = [
                [52, 152, 219],   // Blue
                [46, 204, 113],   // Green
                [155, 89, 182],   // Purple
                [241, 196, 15],   // Yellow
                [230, 126, 34],   // Orange
                [231, 76, 60]     // Red
            ];

            $colorSet = $colors[array_rand($colors)];
            $bgColor = imagecolorallocate($image, $colorSet[0], $colorSet[1], $colorSet[2]);
            $textColor = imagecolorallocate($image, 255, 255, 255);

            // Fill background
            imagefill($image, 0, 0, $bgColor);

            // Add text
            $text = "Demo Image";
            $fontSize = 5;
            $textWidth = imagefontwidth($fontSize) * strlen($text);
            $textHeight = imagefontheight($fontSize);
            $x = ($width - $textWidth) / 2;
            $y = ($height - $textHeight) / 2;

            imagestring($image, $fontSize, $x, $y, $text, $textColor);

            // Capture image data
            ob_start();
            imagejpeg($image, null, 80);
            $imageData = ob_get_contents();
            ob_end_clean();

            // Clean up
            imagedestroy($image);

            return $imageData;

        } catch (Exception $e) {
            // If even fallback image creation fails, return false
            return false;
        }
    }

    /**
     * Download image to temporary location for processing
     */
    private function downloadImageToTemp($url, $filename) {
        try {
            // Create temp directory if it doesn't exist
            $tempDir = '../../uploads/temp';
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $tempPath = $tempDir . '/' . $filename;

            // Download image with fallbacks
            $imageData = $this->downloadImageWithFallbacks($url);

            if ($imageData === false) {
                return false;
            }

            // Save to temp location
            if (file_put_contents($tempPath, $imageData) === false) {
                return false;
            }

            return $tempPath;

        } catch (Exception $e) {
            error_log("Error downloading image to temp: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clean up judging data (assignments and scores)
     */
    private function cleanupJudgingData($verbose = false) {
        try {
            $deletedCounts = [];

            // Get demo show IDs
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            $demoShowIds = [];
            foreach ($demoShowNames as $showName) {
                $stmt = $this->db->getConnection()->prepare("SELECT id FROM shows WHERE name = ?");
                $stmt->execute([$showName]);
                $show = $stmt->fetch(PDO::FETCH_OBJ);
                if ($show) {
                    $demoShowIds[] = $show->id;
                }
            }

            if (empty($demoShowIds)) {
                return $deletedCounts;
            }

            $showIdPlaceholders = implode(',', array_fill(0, count($demoShowIds), '?'));

            // Clean up judge assignments for demo shows
            $stmt = $this->db->getConnection()->prepare("DELETE FROM judge_assignments WHERE show_id IN ({$showIdPlaceholders})");
            $stmt->execute($demoShowIds);
            $deletedCounts['judge_assignments'] = $stmt->rowCount();

            // Clean up judge total scores for demo shows
            $stmt = $this->db->getConnection()->prepare("DELETE FROM judge_total_scores WHERE show_id IN ({$showIdPlaceholders})");
            $stmt->execute($demoShowIds);
            $deletedCounts['judge_total_scores'] = $stmt->rowCount();

            // Clean up judge metric scores for demo shows
            $stmt = $this->db->getConnection()->prepare("DELETE FROM judge_metric_scores WHERE show_id IN ({$showIdPlaceholders})");
            $stmt->execute($demoShowIds);
            $deletedCounts['judge_metric_scores'] = $stmt->rowCount();

            // Clean up judging scores for demo shows (if table exists)
            try {
                $stmt = $this->db->getConnection()->prepare("DELETE FROM judging_scores WHERE registration_id IN (SELECT id FROM registrations WHERE show_id IN ({$showIdPlaceholders}))");
                $stmt->execute($demoShowIds);
                $deletedCounts['judging_scores'] = $stmt->rowCount();
            } catch (Exception $e) {
                // Table might not exist, that's okay
                $deletedCounts['judging_scores'] = 0;
            }

            if ($verbose) {
                $totalDeleted = array_sum($deletedCounts);
                echo "    ✅ Deleted {$totalDeleted} judging records\n";
                foreach ($deletedCounts as $table => $count) {
                    if ($count > 0) {
                        echo "      - {$table}: {$count}\n";
                    }
                }
            }

            return $deletedCounts;

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error cleaning judging data: " . $e->getMessage() . "\n";
            }
            return [];
        }
    }

    /**
     * Ensure judge_assignments table exists with proper structure
     */
    private function ensureJudgeAssignmentsTable() {
        try {
            // Check if the judge_assignments table exists
            $this->db->query("SHOW TABLES LIKE 'judge_assignments'");
            $tableExists = $this->db->rowCount() > 0;

            if (!$tableExists) {
                // Create the table with proper structure
                $this->db->query("CREATE TABLE IF NOT EXISTS `judge_assignments` (
                    `id` int UNSIGNED NOT NULL AUTO_INCREMENT,
                    `show_id` int UNSIGNED NOT NULL,
                    `judge_id` int UNSIGNED NOT NULL,
                    `category_id` int UNSIGNED NULL,
                    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    PRIMARY KEY (`id`),
                    KEY `show_id` (`show_id`),
                    KEY `judge_id` (`judge_id`),
                    KEY `category_id` (`category_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                $this->db->execute();
            } else {
                // Check if updated_at column exists
                $this->db->query("SHOW COLUMNS FROM judge_assignments LIKE 'updated_at'");
                $updatedAtExists = $this->db->rowCount() > 0;

                if (!$updatedAtExists) {
                    // Add updated_at column
                    $this->db->query("ALTER TABLE judge_assignments ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
                    $this->db->execute();
                }

                // Ensure category_id allows NULL
                $this->db->query("SHOW COLUMNS FROM judge_assignments LIKE 'category_id'");
                $column = $this->db->single();

                if ($column && strpos($column->Null, 'YES') === false) {
                    $this->db->query("ALTER TABLE judge_assignments MODIFY category_id int UNSIGNED NULL");
                    $this->db->execute();
                }
            }
        } catch (Exception $e) {
            error_log("Error ensuring judge_assignments table: " . $e->getMessage());
        }
    }

    /**
     * Ensure banner_image column exists in shows table
     */
    private function ensureBannerImageColumn() {
        try {
            // Check if the banner_image column exists
            $this->db->query("SHOW COLUMNS FROM shows LIKE 'banner_image'");
            $result = $this->db->single();

            // If the column doesn't exist, add it
            if (!$result) {
                $this->db->query("ALTER TABLE shows ADD COLUMN banner_image VARCHAR(255) NULL AFTER fan_voting_enabled");
                $this->db->execute();
            }
        } catch (Exception $e) {
            error_log("Error ensuring banner_image column: " . $e->getMessage());
        }
    }

    /**
     * Get the actual filename stored by ImageEditorModel for an entity
     */
    private function getImageFilename($entityType, $entityId) {
        try {
            $stmt = $this->db->getConnection()->prepare(
                "SELECT file_name FROM images WHERE entity_type = ? AND entity_id = ? ORDER BY created_at DESC LIMIT 1"
            );
            $stmt->execute([$entityType, $entityId]);
            $result = $stmt->fetch(PDO::FETCH_OBJ);

            return $result ? $result->file_name : null;

        } catch (Exception $e) {
            error_log("Error getting image filename: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Clear banner images for demo shows
     */
    private function clearShowBannerImages($verbose = false) {
        try {
            // Get demo show names
            $demoShowNames = [
                'Southern California Classic Car Showcase',
                'Miami Beach Exotic Car Festival',
                'Texas Muscle Car Madness',
                'Arizona Desert Classic Concours',
                'Atlanta Import Tuner Expo',
                'Rocky Mountain Vintage Rally',
                'Pacific Northwest Euro Fest',
                'Music City Hot Rod Nationals',
                'Charlotte Motor Speedway Car Show',
                'Las Vegas Strip Supercar Spectacular'
            ];

            $clearedCount = 0;
            foreach ($demoShowNames as $showName) {
                $stmt = $this->db->getConnection()->prepare("UPDATE shows SET banner_image = NULL WHERE name = ?");
                $stmt->execute([$showName]);
                $clearedCount += $stmt->rowCount();
            }

            if ($verbose && $clearedCount > 0) {
                echo "    ✅ Cleared {$clearedCount} banner images from demo shows\n";
            }

        } catch (Exception $e) {
            if ($verbose) {
                echo "    ⚠️ Error clearing banner images: " . $e->getMessage() . "\n";
            }
        }
    }

    /**
     * Set banner image for a show
     */
    private function setShowBannerImage($showId, $filename) {
        try {
            $stmt = $this->db->getConnection()->prepare("UPDATE shows SET banner_image = ? WHERE id = ?");
            $stmt->execute([$filename, $showId]);
        } catch (Exception $e) {
            error_log("Error setting banner image: " . $e->getMessage());
        }
    }
}
