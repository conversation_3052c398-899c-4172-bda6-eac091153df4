<?php
/**
 * Demo Data Generator for Events and Shows Management System
 * 
 * This script generates comprehensive demo data for showcasing the platform to potential customers.
 * Creates realistic shows, events, users, vehicles, registrations, payments, voting, and scoring data.
 * 
 * FEATURES:
 * - 10 realistic car shows with professional descriptions
 * - Coordinators, judges, and participants
 * - Vehicle registrations with detailed descriptions
 * - Complete judging system with scores and comments
 * - Fan voting data
 * - Payment records and transactions
 * - Show categories and judging metrics
 * - Professional, sales-ready demo data
 * 
 * SAFE DOMAINS: Uses test domains (gmai1.com, etc.) to protect real users
 */

// Load configuration
require_once '../../config/config.php';

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Load required classes
require_once '../../core/Database.php';
require_once '../../core/Auth.php';
require_once '../../models/ImageEditorModel.php';

class DemoDataGenerator {
    private $db;
    private $imageModel;
    private $batchSize = 50;
    private $generatedData = [];

    // Safe test domains to prevent affecting real users
    private $testDomains = ['gmai1.com', 'yaho0.com', 'hotmai1.com', 'out1ook.com', 'test-example.com'];

    // Free image sources (using placeholder services and free stock photos)
    private $vehicleImageUrls = [
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop', // Red sports car
        'https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=800&h=600&fit=crop', // Classic car
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop', // Muscle car
        'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=800&h=600&fit=crop', // Porsche
        'https://images.unsplash.com/photo-1580273916550-e323be2ae537?w=800&h=600&fit=crop', // Ferrari
        'https://images.unsplash.com/photo-1555215695-3004980ad54e?w=800&h=600&fit=crop', // BMW
        'https://images.unsplash.com/photo-1542362567-b07e54358753?w=800&h=600&fit=crop', // Lamborghini
        'https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop', // Jaguar
        'https://images.unsplash.com/photo-1525609004556-c46c7d6cf023?w=800&h=600&fit=crop', // Corvette
        'https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?w=800&h=600&fit=crop', // Mercedes
        'https://images.unsplash.com/photo-1549399542-7e3f8b79c341?w=800&h=600&fit=crop', // Shelby Cobra
        'https://images.unsplash.com/photo-1494905998402-395d579af36f?w=800&h=600&fit=crop', // Plymouth
        'https://images.unsplash.com/photo-1605559424843-9e4c228bf1c2?w=800&h=600&fit=crop', // Nissan GTR
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop', // Toyota Supra
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop', // Honda NSX
        'https://images.unsplash.com/photo-1544829099-b9a0c5303bea?w=800&h=600&fit=crop', // McLaren
        'https://images.unsplash.com/photo-1551522435-a13afa10f103?w=800&h=600&fit=crop', // Ford GT
        'https://images.unsplash.com/photo-1563720223185-11003d516935?w=800&h=600&fit=crop', // Aston Martin
        'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=800&h=600&fit=crop', // Maserati
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=800&h=600&fit=crop'  // Alfa Romeo
    ];

    private $showImageUrls = [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=1200&h=800&fit=crop', // Car show venue
        'https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=1200&h=800&fit=crop', // Car exhibition
        'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1200&h=800&fit=crop', // Car meet
        'https://images.unsplash.com/photo-1551522435-a13afa10f103?w=1200&h=800&fit=crop', // Racing event
        'https://images.unsplash.com/photo-1544636331-e26879cd4d9b?w=1200&h=800&fit=crop', // Classic car show
        'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?w=1200&h=800&fit=crop', // Exotic car display
        'https://images.unsplash.com/photo-1583121274602-3e2820c69888?w=1200&h=800&fit=crop', // Vintage rally
        'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1200&h=800&fit=crop', // Muscle car expo
        'https://images.unsplash.com/photo-1580273916550-e323be2ae537?w=1200&h=800&fit=crop', // Import tuner show
        'https://images.unsplash.com/photo-1555215695-3004980ad54e?w=1200&h=800&fit=crop'  // Supercar spectacular
    ];

    public function __construct() {
        $this->db = new Database();
        $this->imageModel = new ImageEditorModel();

        // Set execution limits for large data generation
        ini_set('max_execution_time', 600); // 10 minutes
        ini_set('memory_limit', '512M');

        // Create upload directories if they don't exist
        $this->createUploadDirectories();
    }
    
    /**
     * Generate complete demo dataset
     */
    public function generateCompleteDemo($verbose = true) {
        if ($verbose) {
            echo "🚀 Generating Complete Demo Dataset for Events and Shows Management System\n";
            echo "================================================================\n\n";
        }
        
        $startTime = microtime(true);
        
        try {
            // Step 1: Generate Users (Coordinators, Judges, Participants)
            if ($verbose) echo "👥 Step 1: Generating Users...\n";
            $this->generateUsers($verbose);
            
            // Step 2: Generate Shows
            if ($verbose) echo "\n🏆 Step 2: Generating Car Shows...\n";
            $this->generateShows($verbose);
            
            // Step 3: Generate Show Categories and Judging Metrics
            if ($verbose) echo "\n📋 Step 3: Generating Categories and Judging Metrics...\n";
            $this->generateCategoriesAndMetrics($verbose);
            
            // Step 4: Generate Vehicles
            if ($verbose) echo "\n🚗 Step 4: Generating Vehicles...\n";
            $this->generateVehicles($verbose);
            
            // Step 5: Generate Registrations
            if ($verbose) echo "\n📝 Step 5: Generating Registrations...\n";
            $this->generateRegistrations($verbose);
            
            // Step 6: Generate Payments
            if ($verbose) echo "\n💳 Step 6: Generating Payment Records...\n";
            $this->generatePayments($verbose);
            
            // Step 7: Generate Judging Scores
            if ($verbose) echo "\n⭐ Step 7: Generating Judging Scores...\n";
            $this->generateJudgingScores($verbose);
            
            // Step 8: Generate Fan Votes
            if ($verbose) echo "\n👍 Step 8: Generating Fan Votes...\n";
            $this->generateFanVotes($verbose);

            // Step 9: Download and Add Images
            if ($verbose) echo "\n📸 Step 9: Downloading and Adding Images...\n";
            $this->generateImages($verbose);

            $totalTime = round(microtime(true) - $startTime, 2);
            
            if ($verbose) {
                echo "\n✅ Demo Data Generation Complete!\n";
                echo "================================================================\n";
                echo "Total Generation Time: {$totalTime} seconds\n";
                echo "\nGenerated Data Summary:\n";
                foreach ($this->generatedData as $type => $count) {
                    echo "- {$type}: {$count}\n";
                }
                echo "\n🎯 Your demo site is now ready to showcase to potential customers!\n";
            }
            
            return true;
            
        } catch (Exception $e) {
            if ($verbose) {
                echo "\n❌ Error generating demo data: " . $e->getMessage() . "\n";
                echo $e->getTraceAsString() . "\n";
            }
            return false;
        }
    }
    
    /**
     * Generate realistic users for the demo
     */
    private function generateUsers($verbose = true) {
        $users = [
            // Coordinators
            ['name' => 'Michael Rodriguez', 'email' => '<EMAIL>', 'role' => 'coordinator', 'phone' => '(*************'],
            ['name' => 'Sarah Thompson', 'email' => '<EMAIL>', 'role' => 'coordinator', 'phone' => '(*************'],
            ['name' => 'David Chen', 'email' => '<EMAIL>', 'role' => 'coordinator', 'phone' => '(*************'],
            
            // Judges
            ['name' => 'Robert Wilson', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            ['name' => 'Jennifer Davis', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            ['name' => 'Mark Johnson', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            ['name' => 'Lisa Anderson', 'email' => '<EMAIL>', 'role' => 'judge', 'phone' => '(*************'],
            
            // Participants
            ['name' => 'James Miller', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Maria Garcia', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Christopher Brown', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Amanda Taylor', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Daniel Martinez', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Jessica White', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Kevin Lee', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Rachel Green', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Steven Clark', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Nicole Adams', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Brian Turner', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Michelle Scott', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Ryan Phillips', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
            ['name' => 'Stephanie King', 'email' => '<EMAIL>', 'role' => 'user', 'phone' => '(*************'],
        ];
        
        $cities = ['Los Angeles', 'Miami', 'Austin', 'Phoenix', 'Atlanta', 'Denver', 'Seattle', 'Nashville', 'Charlotte', 'Las Vegas'];
        $states = ['CA', 'FL', 'TX', 'AZ', 'GA', 'CO', 'WA', 'TN', 'NC', 'NV'];
        
        $generated = 0;
        foreach ($users as $index => $userData) {
            $city = $cities[array_rand($cities)];
            $state = $states[array_rand($states)];
            $address = (1000 + $index) . ' Demo Street';
            $zip = sprintf('%05d', rand(10000, 99999));
            
            $sql = "INSERT INTO users (name, email, password, role, status, phone, address, city, state, zip, created_at) 
                    VALUES (?, ?, ?, ?, 'active', ?, ?, ?, ?, ?, NOW())";
            
            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->execute([
                $userData['name'],
                $userData['email'],
                password_hash('demo123', PASSWORD_DEFAULT),
                $userData['role'],
                $userData['phone'],
                $address,
                $city,
                $state,
                $zip
            ]);
            
            $generated++;
            
            if ($verbose && $generated % 5 == 0) {
                echo "  Generated {$generated} users...\n";
            }
        }
        
        $this->generatedData['Users'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} demo users\n";
    }
    
    /**
     * Clean up demo data (removes only demo users with test domains)
     */
    public function cleanupDemoData($verbose = true) {
        if ($verbose) {
            echo "🧹 Cleaning up demo data...\n";
        }
        
        try {
            $domainConditions = array_map(function($domain) {
                return "email LIKE '%@{$domain}'";
            }, $this->testDomains);
            $whereClause = "(" . implode(' OR ', $domainConditions) . ")";
            
            // Count demo users to be deleted
            $this->db->query("SELECT COUNT(*) as count FROM users WHERE " . $whereClause);
            $result = $this->db->single();
            $demoUsers = $result->count ?? 0;
            
            if ($demoUsers > 0) {
                // Delete demo users (this will cascade to related data)
                $this->db->query("DELETE FROM users WHERE " . $whereClause);
                $deleted = $this->db->execute() ? $this->db->rowCount() : 0;
                
                if ($verbose) {
                    echo "  ✅ Deleted {$deleted} demo users and all related data\n";
                }
                
                return $deleted;
            } else {
                if ($verbose) {
                    echo "  ℹ️ No demo data found to clean up\n";
                }
                return 0;
            }
            
        } catch (Exception $e) {
            if ($verbose) {
                echo "  ❌ Error cleaning up demo data: " . $e->getMessage() . "\n";
            }
            return 0;
        }
    }

    /**
     * Generate realistic car shows
     */
    private function generateShows($verbose = true) {
        // Get coordinator IDs
        $this->db->query("SELECT id FROM users WHERE role = 'coordinator' AND email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' LIMIT 3");
        $coordinators = $this->db->resultSet();

        $shows = [
            [
                'name' => 'Southern California Classic Car Showcase',
                'description' => 'Join us for the premier classic car event on the West Coast! Featuring vintage automobiles from 1920-1980, this prestigious show attracts collectors and enthusiasts from across the nation. Professional judging, awards ceremony, and family-friendly activities. Food trucks, live music, and vendor booths available.',
                'location' => 'Exposition Park, Los Angeles, CA',
                'start_date' => '2025-08-15',
                'end_date' => '2025-08-17',
                'registration_fee' => 45.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Miami Beach Exotic Car Festival',
                'description' => 'Experience the ultimate luxury car event in beautiful Miami Beach! Showcasing supercars, hypercars, and exotic vehicles from around the world. VIP experiences available. Professional photography, media coverage, and networking opportunities with industry leaders.',
                'location' => 'Miami Beach Convention Center, Miami, FL',
                'start_date' => '2025-09-20',
                'end_date' => '2025-09-22',
                'registration_fee' => 75.00,
                'listing_fee' => 35.00
            ],
            [
                'name' => 'Texas Muscle Car Madness',
                'description' => 'Rev up for the biggest muscle car event in Texas! Featuring American muscle from the golden era - Mustangs, Camaros, Challengers, and more. Drag racing demonstrations, burnout contests, and live entertainment. BBQ food vendors and family activities.',
                'location' => 'Circuit of the Americas, Austin, TX',
                'start_date' => '2025-10-05',
                'end_date' => '2025-10-06',
                'registration_fee' => 35.00,
                'listing_fee' => 20.00
            ],
            [
                'name' => 'Arizona Desert Classic Concours',
                'description' => 'An elegant concours d\'elegance in the stunning Arizona desert. Featuring meticulously restored classics, rare European sports cars, and American luxury vehicles. Professional judging by certified experts. Awards banquet and charity auction.',
                'location' => 'Scottsdale Civic Center, Scottsdale, AZ',
                'start_date' => '2025-11-10',
                'end_date' => '2025-11-12',
                'registration_fee' => 65.00,
                'listing_fee' => 30.00
            ],
            [
                'name' => 'Atlanta Import Tuner Expo',
                'description' => 'The Southeast\'s premier import and tuner car show! Featuring modified Japanese, European, and domestic vehicles. Dyno competitions, sound-off contests, and drift demonstrations. Vendor marketplace with the latest aftermarket parts and accessories.',
                'location' => 'Georgia World Congress Center, Atlanta, GA',
                'start_date' => '2025-07-25',
                'end_date' => '2025-07-27',
                'registration_fee' => 40.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Rocky Mountain Vintage Rally',
                'description' => 'A scenic mountain rally featuring vintage and classic vehicles. Driving tours through Colorado\'s beautiful landscapes, technical sessions, and social events. Perfect for vintage car enthusiasts who love to drive their classics.',
                'location' => 'Denver Convention Center, Denver, CO',
                'start_date' => '2025-08-30',
                'end_date' => '2025-09-01',
                'registration_fee' => 55.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Pacific Northwest Euro Fest',
                'description' => 'Celebrating European automotive excellence in the beautiful Pacific Northwest. BMW, Mercedes-Benz, Audi, Porsche, and more. Technical seminars, parts swap meet, and scenic drives. Coffee and craft beer garden.',
                'location' => 'Seattle Center, Seattle, WA',
                'start_date' => '2025-09-15',
                'end_date' => '2025-09-16',
                'registration_fee' => 50.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Music City Hot Rod Nationals',
                'description' => 'Nashville\'s hottest rod and custom car event! Traditional hot rods, street rods, and custom builds. Live country music, pin-up contests, and vintage vendor booths. Family-friendly with activities for all ages.',
                'location' => 'Nashville Fairgrounds, Nashville, TN',
                'start_date' => '2025-10-20',
                'end_date' => '2025-10-21',
                'registration_fee' => 30.00,
                'listing_fee' => 20.00
            ],
            [
                'name' => 'Charlotte Motor Speedway Car Show',
                'description' => 'Experience automotive excellence at the legendary Charlotte Motor Speedway! All makes and models welcome. Track tours, racing simulators, and meet-and-greets with NASCAR drivers. Professional photography and awards ceremony.',
                'location' => 'Charlotte Motor Speedway, Charlotte, NC',
                'start_date' => '2025-11-25',
                'end_date' => '2025-11-26',
                'registration_fee' => 45.00,
                'listing_fee' => 25.00
            ],
            [
                'name' => 'Las Vegas Strip Supercar Spectacular',
                'description' => 'The most glamorous supercar event in Las Vegas! Featuring the world\'s most exclusive and expensive vehicles. Red carpet arrivals, celebrity appearances, and VIP experiences. Night cruise down the famous Las Vegas Strip.',
                'location' => 'Las Vegas Convention Center, Las Vegas, NV',
                'start_date' => '2025-12-10',
                'end_date' => '2025-12-12',
                'registration_fee' => 100.00,
                'listing_fee' => 50.00
            ]
        ];

        $generated = 0;
        foreach ($shows as $index => $showData) {
            $coordinatorId = $coordinators[$index % count($coordinators)]->id;

            // Calculate registration dates (30-60 days before show)
            $startDate = new DateTime($showData['start_date']);
            $regStart = clone $startDate;
            $regStart->modify('-60 days');
            $regEnd = clone $startDate;
            $regEnd->modify('-7 days');

            $sql = "INSERT INTO shows (name, description, location, start_date, end_date, registration_start, registration_end,
                    coordinator_id, status, fan_voting_enabled, registration_fee, listing_fee, listing_paid, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'published', 1, ?, ?, 1, NOW())";

            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->execute([
                $showData['name'],
                $showData['description'],
                $showData['location'],
                $showData['start_date'],
                $showData['end_date'],
                $regStart->format('Y-m-d'),
                $regEnd->format('Y-m-d'),
                $coordinatorId,
                $showData['registration_fee'],
                $showData['listing_fee']
            ]);

            $generated++;

            if ($verbose && $generated % 3 == 0) {
                echo "  Generated {$generated} shows...\n";
            }
        }

        $this->generatedData['Shows'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} professional car shows\n";
    }

    /**
     * Generate show categories and judging metrics
     */
    private function generateCategoriesAndMetrics($verbose = true) {
        // Get all show IDs
        $this->db->query("SELECT id FROM shows ORDER BY id");
        $shows = $this->db->resultSet();

        $categories = [
            ['name' => 'Classic American', 'description' => 'American classics from 1920-1980'],
            ['name' => 'European Sports Cars', 'description' => 'European sports and luxury vehicles'],
            ['name' => 'Muscle Cars', 'description' => 'American muscle cars from the golden era'],
            ['name' => 'Import Tuners', 'description' => 'Modified import and tuner vehicles'],
            ['name' => 'Exotic Supercars', 'description' => 'High-end exotic and supercar vehicles'],
            ['name' => 'Hot Rods & Customs', 'description' => 'Traditional hot rods and custom builds'],
            ['name' => 'Vintage Racing', 'description' => 'Historic racing and competition vehicles']
        ];

        $metrics = [
            ['name' => 'Exterior Condition', 'description' => 'Paint, body, chrome, and overall exterior appearance', 'max_score' => 25, 'weight' => 1.0],
            ['name' => 'Interior Quality', 'description' => 'Seats, dashboard, trim, and interior components', 'max_score' => 20, 'weight' => 0.8],
            ['name' => 'Engine Bay', 'description' => 'Engine cleanliness, originality, and presentation', 'max_score' => 20, 'weight' => 0.9],
            ['name' => 'Undercarriage', 'description' => 'Chassis, suspension, exhaust, and underside condition', 'max_score' => 15, 'weight' => 0.7],
            ['name' => 'Authenticity', 'description' => 'Period correctness and original components', 'max_score' => 10, 'weight' => 1.2],
            ['name' => 'Craftsmanship', 'description' => 'Quality of restoration or modification work', 'max_score' => 10, 'weight' => 1.1]
        ];

        $categoriesGenerated = 0;
        $metricsGenerated = 0;

        foreach ($shows as $show) {
            // Generate 4-5 categories per show
            $showCategories = array_slice($categories, 0, rand(4, 5));

            foreach ($showCategories as $index => $categoryData) {
                $sql = "INSERT INTO show_categories (show_id, name, description, display_order, created_at)
                        VALUES (?, ?, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $show->id,
                    $categoryData['name'],
                    $categoryData['description'],
                    $index + 1
                ]);

                $categoriesGenerated++;
            }

            // Generate judging metrics for each show
            foreach ($metrics as $index => $metricData) {
                $sql = "INSERT INTO judging_metrics (show_id, name, description, max_score, weight, display_order, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $show->id,
                    $metricData['name'],
                    $metricData['description'],
                    $metricData['max_score'],
                    $metricData['weight'],
                    $index + 1
                ]);

                $metricsGenerated++;
            }
        }

        $this->generatedData['Categories'] = $categoriesGenerated;
        $this->generatedData['Judging Metrics'] = $metricsGenerated;
        if ($verbose) echo "  ✅ Generated {$categoriesGenerated} categories and {$metricsGenerated} judging metrics\n";
    }

    /**
     * Generate realistic vehicles
     */
    private function generateVehicles($verbose = true) {
        // Get user IDs (participants)
        $this->db->query("SELECT id FROM users WHERE role = 'user' AND (email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com')");
        $users = $this->db->resultSet();

        $vehicles = [
            ['make' => 'Ford', 'model' => 'Mustang GT', 'year' => 1967, 'color' => 'Candy Apple Red', 'description' => 'Fully restored 1967 Mustang GT with 390 V8 engine. Numbers matching, concours quality restoration with original interior and chrome. Recent frame-off restoration completed in 2023.'],
            ['make' => 'Chevrolet', 'model' => 'Camaro SS', 'year' => 1969, 'color' => 'Rally Green', 'description' => 'Stunning 1969 Camaro SS with 396 big block. Professional restoration with period-correct Rally wheels. Show quality paint and interior. Documented with build sheet and Protect-O-Plate.'],
            ['make' => 'Dodge', 'model' => 'Challenger R/T', 'year' => 1970, 'color' => 'Plum Crazy Purple', 'description' => 'Rare 1970 Challenger R/T 440 Six Pack. Rotisserie restoration with correct Plum Crazy paint. Numbers matching drivetrain with Shaker hood. Investment grade muscle car.'],
            ['make' => 'Porsche', 'model' => '911 Turbo', 'year' => 1987, 'color' => 'Guards Red', 'description' => 'Exceptional 1987 Porsche 911 Turbo with only 45,000 original miles. Maintained by Porsche specialists, all service records available. Original paint, leather interior in excellent condition.'],
            ['make' => 'Ferrari', 'model' => '308 GTS', 'year' => 1979, 'color' => 'Rosso Corsa', 'description' => 'Beautiful 1979 Ferrari 308 GTS with removable targa top. Recent major service including timing belt and clutch. Stunning example of this iconic 1980s supercar with proper documentation.'],
            ['make' => 'BMW', 'model' => 'M3', 'year' => 1988, 'color' => 'Alpine White', 'description' => 'Pristine 1988 BMW E30 M3 with S14 engine. One of the most sought-after BMWs ever made. Completely stock with original BBS wheels. Garage kept with meticulous maintenance records.'],
            ['make' => 'Lamborghini', 'model' => 'Countach', 'year' => 1985, 'color' => 'Bianco Polo', 'description' => 'Iconic 1985 Lamborghini Countach LP5000 QV. The poster car of the 1980s in stunning white. Recent service by Lamborghini specialists. Extremely rare and appreciating classic supercar.'],
            ['make' => 'Jaguar', 'model' => 'E-Type', 'year' => 1963, 'color' => 'British Racing Green', 'description' => 'Gorgeous 1963 Jaguar E-Type Series 1 roadster. Enzo Ferrari called it "the most beautiful car ever made." Matching numbers with extensive restoration documentation.'],
            ['make' => 'Corvette', 'model' => 'Stingray', 'year' => 1963, 'color' => 'Riverside Red', 'description' => 'Split-window 1963 Corvette Stingray coupe. One year only design with fuel injection. Concours restoration with correct paint and interior. NCRS Top Flight award winner.'],
            ['make' => 'Mercedes-Benz', 'model' => '300SL Gullwing', 'year' => 1955, 'color' => 'Silver Metallic', 'description' => 'Legendary 1955 Mercedes-Benz 300SL Gullwing coupe. One of the most desirable classics ever built. Matching numbers with comprehensive restoration. Museum quality example.'],
            ['make' => 'Shelby', 'model' => 'Cobra 427', 'year' => 1965, 'color' => 'Guardsman Blue', 'description' => 'Authentic 1965 Shelby Cobra 427 S/C. The ultimate American sports car with massive 427 side-oiler engine. Documented history with Shelby American registry verification.'],
            ['make' => 'Plymouth', 'model' => 'Barracuda', 'year' => 1970, 'color' => 'In-Violet', 'description' => 'Rare 1970 Plymouth \'Cuda with 440 Six Pack engine. High Impact In-Violet paint with black interior. Numbers matching with broadcast sheet. Professionally restored to concours standards.'],
            ['make' => 'Nissan', 'model' => 'Skyline GT-R', 'year' => 1995, 'color' => 'Midnight Purple', 'description' => 'JDM 1995 Nissan Skyline GT-R R33 in rare Midnight Purple. RB26DETT twin-turbo engine with all-wheel drive. Recently imported and federalized. Enthusiast owned with modifications.'],
            ['make' => 'Toyota', 'model' => 'Supra Turbo', 'year' => 1994, 'color' => 'Renaissance Red', 'description' => 'Pristine 1994 Toyota Supra Turbo with 6-speed manual transmission. Stock 2JZ-GTE engine with massive potential. Garage kept with only 65,000 original miles.'],
            ['make' => 'Honda', 'model' => 'NSX', 'year' => 1991, 'color' => 'Formula Red', 'description' => 'Exceptional 1991 Acura NSX in Formula Red with black interior. Honda\'s supercar with mid-mounted VTEC V6. Meticulously maintained with all service records. Future classic.'],
            ['make' => 'McLaren', 'model' => 'F1', 'year' => 1995, 'color' => 'Carbon Fiber', 'description' => 'Ultra-rare 1995 McLaren F1 road car. Only 106 ever built. Central driving position with gold-lined engine bay. The ultimate analog supercar and automotive masterpiece.'],
            ['make' => 'Ford', 'model' => 'GT40', 'year' => 1966, 'color' => 'Gulf Racing Blue', 'description' => 'Legendary 1966 Ford GT40 Mk I in iconic Gulf Racing livery. Le Mans-winning design with 289 V8 engine. Documented racing history and provenance. Investment grade race car.'],
            ['make' => 'Aston Martin', 'model' => 'DB5', 'year' => 1964, 'color' => 'Silver Birch', 'description' => 'Iconic 1964 Aston Martin DB5 in Silver Birch with red leather interior. The James Bond car with timeless British elegance. Matching numbers with comprehensive service history.'],
            ['make' => 'Maserati', 'model' => 'Ghibli', 'year' => 1969, 'color' => 'Blu Sera', 'description' => 'Stunning 1969 Maserati Ghibli with 4.7L V8 engine. Giugiaro-designed masterpiece with incredible Italian styling. Recent restoration by Maserati specialists.'],
            ['make' => 'Alfa Romeo', 'model' => 'Spider', 'year' => 1967, 'color' => 'Alfa Red', 'description' => 'Beautiful 1967 Alfa Romeo Spider Duetto. The Graduate car with timeless Italian design. Matching numbers with recent mechanical restoration. Perfect weekend touring car.']
        ];

        $generated = 0;
        foreach ($vehicles as $index => $vehicleData) {
            $ownerId = $users[$index % count($users)]->id;
            $licensePlate = 'DEMO' . sprintf('%03d', $index + 1);
            $vin = 'DEMO' . str_pad($index + 1, 13, '0', STR_PAD_LEFT);

            $sql = "INSERT INTO vehicles (owner_id, make, model, year, color, license_plate, vin, description, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())";

            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->execute([
                $ownerId,
                $vehicleData['make'],
                $vehicleData['model'],
                $vehicleData['year'],
                $vehicleData['color'],
                $licensePlate,
                $vin,
                $vehicleData['description']
            ]);

            $generated++;

            if ($verbose && $generated % 5 == 0) {
                echo "  Generated {$generated} vehicles...\n";
            }
        }

        $this->generatedData['Vehicles'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} stunning demo vehicles\n";
    }

    /**
     * Generate vehicle registrations for shows
     */
    private function generateRegistrations($verbose = true) {
        // Get shows, vehicles, and categories
        $this->db->query("SELECT id FROM shows ORDER BY id");
        $shows = $this->db->resultSet();

        $this->db->query("SELECT id, owner_id FROM vehicles ORDER BY id");
        $vehicles = $this->db->resultSet();

        $generated = 0;

        foreach ($shows as $show) {
            // Get categories for this show
            $this->db->query("SELECT id FROM show_categories WHERE show_id = :show_id");
            $this->db->bind(':show_id', $show->id);
            $categories = $this->db->resultSet();

            if (empty($categories)) continue;

            // Register 8-12 vehicles per show
            $vehiclesToRegister = array_slice($vehicles, 0, rand(8, 12));

            foreach ($vehiclesToRegister as $vehicle) {
                $categoryId = $categories[array_rand($categories)]->id;
                $registrationNumber = 'RER-' . str_pad($generated + 1, 6, '0', STR_PAD_LEFT);
                $displayNumber = sprintf('%03d', ($generated % 999) + 1);

                $sql = "INSERT INTO registrations (show_id, vehicle_id, owner_id, category_id, status, payment_status,
                        fee, registration_number, display_number, created_at)
                        VALUES (?, ?, ?, ?, 'approved', 'completed', 45.00, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $show->id,
                    $vehicle->id,
                    $vehicle->owner_id,
                    $categoryId,
                    $registrationNumber,
                    $displayNumber
                ]);

                $generated++;
            }

            if ($verbose && $generated % 10 == 0) {
                echo "  Generated {$generated} registrations...\n";
            }
        }

        $this->generatedData['Registrations'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} vehicle registrations\n";
    }

    /**
     * Generate payment records
     */
    private function generatePayments($verbose = true) {
        // Get all registrations
        $this->db->query("SELECT r.id, r.owner_id, r.fee, s.name as show_name
                          FROM registrations r
                          JOIN shows s ON r.show_id = s.id
                          ORDER BY r.id");
        $registrations = $this->db->resultSet();

        $paymentMethods = ['Credit Card', 'PayPal', 'Bank Transfer', 'Cash'];
        $generated = 0;

        foreach ($registrations as $registration) {
            $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
            $reference = 'PAY-' . strtoupper(substr(md5($registration->id . time()), 0, 10));

            $sql = "INSERT INTO payments (user_id, amount, payment_status, payment_reference, payment_type,
                    related_id, notes, created_at)
                    VALUES (?, ?, 'completed', ?, 'registration', ?, ?, NOW())";

            $stmt = $this->db->getConnection()->prepare($sql);
            $stmt->execute([
                $registration->owner_id,
                $registration->fee,
                $reference,
                $registration->id,
                "Registration payment for {$registration->show_name} - {$paymentMethod}"
            ]);

            $generated++;

            if ($verbose && $generated % 20 == 0) {
                echo "  Generated {$generated} payments...\n";
            }
        }

        $this->generatedData['Payments'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} payment records\n";
    }

    /**
     * Generate judging scores with realistic comments
     */
    private function generateJudgingScores($verbose = true) {
        // Get judges
        $this->db->query("SELECT id FROM users WHERE role = 'judge' AND (email LIKE '%@gmai1.com' OR email LIKE '%@yaho0.com' OR email LIKE '%@hotmai1.com' OR email LIKE '%@out1ook.com' OR email LIKE '%@test-example.com')");
        $judges = $this->db->resultSet();

        // Get all registrations with show info
        $this->db->query("SELECT r.id as registration_id, r.show_id FROM registrations r ORDER BY r.id");
        $registrations = $this->db->resultSet();

        $comments = [
            'Exterior Condition' => [
                'Exceptional paint quality with deep, lustrous finish. Chrome is flawless.',
                'Outstanding restoration work. Paint depth and clarity are museum quality.',
                'Beautiful color choice with excellent execution. Minor stone chips noted.',
                'Impressive attention to detail. Panel gaps are perfect throughout.'
            ],
            'Interior Quality' => [
                'Original interior in remarkable condition. Seats show minimal wear.',
                'Beautiful restoration with period-correct materials and colors.',
                'Dashboard and trim pieces are in excellent condition.',
                'Upholstery work is professional grade. Attention to detail is evident.'
            ],
            'Engine Bay' => [
                'Engine bay is detailed to concours standards. Everything is correct.',
                'Impressive presentation with proper finishes and correct components.',
                'Clean, organized layout with attention to period-correct details.',
                'Engine appears to be numbers matching with proper date codes.'
            ],
            'Undercarriage' => [
                'Underside is detailed and clean. No rust or corrosion visible.',
                'Suspension components appear original and properly finished.',
                'Exhaust system is correct for the application.',
                'Frame and chassis show excellent restoration work.'
            ],
            'Authenticity' => [
                'All components appear period-correct and properly dated.',
                'Excellent research evident in component selection and finishes.',
                'Documentation supports authenticity claims.',
                'Rare options are properly represented and functional.'
            ],
            'Craftsmanship' => [
                'Restoration work is of the highest professional standard.',
                'Attention to detail is evident throughout the vehicle.',
                'Fit and finish exceed factory specifications.',
                'Workmanship demonstrates master-level skills.'
            ]
        ];

        $generated = 0;

        foreach ($registrations as $registration) {
            // Get judging metrics for this show
            $this->db->query("SELECT id, name, max_score FROM judging_metrics WHERE show_id = :show_id ORDER BY id");
            $this->db->bind(':show_id', $registration->show_id);
            $metrics = $this->db->resultSet();

            if (empty($metrics)) continue;

            // Assign 2-3 judges per registration
            $assignedJudges = array_slice($judges, 0, rand(2, 3));

            foreach ($assignedJudges as $judge) {
                foreach ($metrics as $metric) {
                    // Generate realistic scores (70-100% of max score)
                    $minScore = $metric->max_score * 0.7;
                    $maxScore = $metric->max_score;
                    $score = round(rand($minScore * 100, $maxScore * 100) / 100, 2);

                    // Get appropriate comment
                    $metricComments = $comments[$metric->name] ?? ['Excellent work overall.'];
                    $comment = $metricComments[array_rand($metricComments)];

                    $sql = "INSERT INTO judging_scores (registration_id, judge_id, metric_id, score, comments, is_draft, created_at)
                            VALUES (?, ?, ?, ?, ?, 0, NOW())";

                    $stmt = $this->db->getConnection()->prepare($sql);
                    $stmt->execute([
                        $registration->registration_id,
                        $judge->id,
                        $metric->id,
                        $score,
                        $comment
                    ]);

                    $generated++;
                }
            }

            if ($verbose && $generated % 50 == 0) {
                echo "  Generated {$generated} judging scores...\n";
            }
        }

        $this->generatedData['Judging Scores'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} professional judging scores\n";
    }

    /**
     * Generate fan votes
     */
    private function generateFanVotes($verbose = true) {
        // Get all registrations
        $this->db->query("SELECT id, show_id FROM registrations ORDER BY id");
        $registrations = $this->db->resultSet();

        $generated = 0;

        foreach ($registrations as $registration) {
            // Generate 5-25 fan votes per vehicle
            $voteCount = rand(5, 25);

            for ($i = 0; $i < $voteCount; $i++) {
                // Generate unique IP addresses
                $ip = rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);

                $sql = "INSERT IGNORE INTO fan_votes (show_id, registration_id, voter_ip, created_at)
                        VALUES (?, ?, ?, NOW())";

                $stmt = $this->db->getConnection()->prepare($sql);
                $stmt->execute([
                    $registration->show_id,
                    $registration->id,
                    $ip
                ]);

                $generated++;
            }

            if ($verbose && $generated % 100 == 0) {
                echo "  Generated {$generated} fan votes...\n";
            }
        }

        $this->generatedData['Fan Votes'] = $generated;
        if ($verbose) echo "  ✅ Generated {$generated} fan votes\n";
    }

    /**
     * Create upload directories if they don't exist
     */
    private function createUploadDirectories() {
        $directories = [
            '../../uploads',
            '../../uploads/vehicles',
            '../../uploads/shows',
            '../../uploads/temp'
        ];

        foreach ($directories as $dir) {
            if (!file_exists($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    /**
     * Download image from URL and save to local storage
     *
     * @param string $url Image URL
     * @param string $filename Local filename
     * @param string $directory Upload directory
     * @return string|false Local file path or false on failure
     */
    private function downloadImage($url, $filename, $directory) {
        try {
            // Create directory if it doesn't exist
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }

            $localPath = $directory . '/' . $filename;

            // Download image with timeout and user agent
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'user_agent' => 'Mozilla/5.0 (compatible; DemoDataGenerator/1.0)'
                ]
            ]);

            $imageData = file_get_contents($url, false, $context);

            if ($imageData === false) {
                return false;
            }

            // Save image
            if (file_put_contents($localPath, $imageData) === false) {
                return false;
            }

            return $localPath;

        } catch (Exception $e) {
            error_log("Error downloading image from {$url}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate and download images for vehicles and shows
     */
    private function generateImages($verbose = true) {
        $vehicleImages = 0;
        $showImages = 0;

        try {
            // Generate vehicle images
            if ($verbose) echo "  📱 Downloading vehicle images...\n";

            $this->db->query("SELECT id FROM vehicles ORDER BY id");
            $vehicles = $this->db->resultSet();

            foreach ($vehicles as $index => $vehicle) {
                if ($index >= count($this->vehicleImageUrls)) break;

                $imageUrl = $this->vehicleImageUrls[$index];
                $filename = 'demo_vehicle_' . $vehicle->id . '_' . time() . '.jpg';
                $directory = '../../uploads/vehicles';

                $localPath = $this->downloadImage($imageUrl, $filename, $directory);

                if ($localPath) {
                    // Get image dimensions
                    $imageInfo = getimagesize($localPath);
                    $width = $imageInfo[0] ?? 800;
                    $height = $imageInfo[1] ?? 600;
                    $fileSize = filesize($localPath);

                    // Add to images table
                    $imageData = [
                        'user_id' => 1, // Admin user
                        'entity_type' => 'vehicle',
                        'entity_id' => $vehicle->id,
                        'file_name' => $filename,
                        'file_path' => 'uploads/vehicles/' . $filename,
                        'file_size' => $fileSize,
                        'file_type' => 'image/jpeg',
                        'width' => $width,
                        'height' => $height,
                        'is_primary' => 1,
                        'optimized' => 0
                    ];

                    if ($this->imageModel->addImage($imageData)) {
                        $vehicleImages++;

                        if ($verbose && $vehicleImages % 5 == 0) {
                            echo "    Downloaded {$vehicleImages} vehicle images...\n";
                        }
                    } else {
                        // Clean up file if database insert failed
                        unlink($localPath);
                    }
                } else {
                    if ($verbose) {
                        echo "    ⚠️ Failed to download image for vehicle {$vehicle->id}\n";
                    }
                }

                // Small delay to be respectful to image servers
                usleep(500000); // 0.5 seconds
            }

            // Generate show images
            if ($verbose) echo "  🏆 Downloading show images...\n";

            $this->db->query("SELECT id FROM shows ORDER BY id");
            $shows = $this->db->resultSet();

            foreach ($shows as $index => $show) {
                if ($index >= count($this->showImageUrls)) break;

                $imageUrl = $this->showImageUrls[$index];
                $filename = 'demo_show_' . $show->id . '_' . time() . '.jpg';
                $directory = '../../uploads/shows';

                $localPath = $this->downloadImage($imageUrl, $filename, $directory);

                if ($localPath) {
                    // Get image dimensions
                    $imageInfo = getimagesize($localPath);
                    $width = $imageInfo[0] ?? 1200;
                    $height = $imageInfo[1] ?? 800;
                    $fileSize = filesize($localPath);

                    // Add to images table
                    $imageData = [
                        'user_id' => 1, // Admin user
                        'entity_type' => 'show',
                        'entity_id' => $show->id,
                        'file_name' => $filename,
                        'file_path' => 'uploads/shows/' . $filename,
                        'file_size' => $fileSize,
                        'file_type' => 'image/jpeg',
                        'width' => $width,
                        'height' => $height,
                        'is_primary' => 1,
                        'optimized' => 0
                    ];

                    if ($this->imageModel->addImage($imageData)) {
                        $showImages++;

                        if ($verbose && $showImages % 3 == 0) {
                            echo "    Downloaded {$showImages} show images...\n";
                        }
                    } else {
                        // Clean up file if database insert failed
                        unlink($localPath);
                    }
                } else {
                    if ($verbose) {
                        echo "    ⚠️ Failed to download image for show {$show->id}\n";
                    }
                }

                // Small delay to be respectful to image servers
                usleep(500000); // 0.5 seconds
            }

            $this->generatedData['Vehicle Images'] = $vehicleImages;
            $this->generatedData['Show Images'] = $showImages;

            if ($verbose) {
                echo "  ✅ Downloaded {$vehicleImages} vehicle images and {$showImages} show images\n";
            }

        } catch (Exception $e) {
            if ($verbose) {
                echo "  ❌ Error downloading images: " . $e->getMessage() . "\n";
            }
            error_log("Demo image generation error: " . $e->getMessage());
        }
    }
}
