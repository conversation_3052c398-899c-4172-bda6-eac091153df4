<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Registration Details</h1>
                <div class="btn-group" role="group">
                    <?php if (isset($_SESSION['user_role']) && ($_SESSION['user_role'] === 'admin' || $_SESSION['user_role'] === 'coordinator')): ?>
                        <a href="<?php echo BASE_URL; ?>/registration/printRegistration/<?php echo $registration->id; ?>" 
                           class="btn btn-primary" target="_blank">
                            <i class="fas fa-print"></i> Print Registration
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo BASE_URL; ?>/user/registrations" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Registrations
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Registration Information -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clipboard-list"></i> Registration Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Registration #:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($registration->registration_number ?? $registration->id); ?></span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Display #:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($registration->display_number ?? ($registration->registration_number ?? $registration->id)); ?></span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Registration Date:</strong><br>
                            <span class="text-muted"><?php echo !empty($registration->created_at) ? formatDateTimeForUser($registration->created_at, $_SESSION['user_id'] ?? null, 'F j, Y g:i A') : 'N/A'; ?></span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Status:</strong><br>
                            <span class="badge badge-<?php echo ($registration->status === 'confirmed') ? 'success' : (($registration->status === 'pending') ? 'warning' : 'secondary'); ?>">
                                <?php echo ucfirst($registration->status ?? 'N/A'); ?>
                            </span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Payment Status:</strong><br>
                            <span class="badge badge-<?php echo ($registration->payment_status === 'paid') ? 'success' : (($registration->payment_status === 'pending') ? 'warning' : 'danger'); ?>">
                                <?php echo ucfirst($registration->payment_status ?? 'N/A'); ?>
                            </span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Payment Method:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($registration->payment_method ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <?php if (!empty($registration->payment_amount)): ?>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Payment Amount:</strong><br>
                            <span class="text-muted">$<?php echo number_format($registration->payment_amount, 2); ?></span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Check-in Status:</strong><br>
                            <span class="badge badge-<?php echo $registration->checked_in ? 'success' : 'secondary'; ?>">
                                <?php echo $registration->checked_in ? 'Checked In' : 'Not Checked In'; ?>
                            </span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Show Information -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt"></i> Show Information
                    </h5>
                </div>
                <div class="card-body">
                    <h6><?php echo htmlspecialchars($show->name ?? 'N/A'); ?></h6>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Date:</strong><br>
                            <span class="text-muted">
                                <?php 
                                if (!empty($show->event_date)) {
                                    echo formatDateTimeForUser($show->event_date, $user->id ?? null, 'F j, Y');
                                } elseif (!empty($show->start_date)) {
                                    echo formatDateTimeForUser($show->start_date, $user->id ?? null, 'F j, Y');
                                } else {
                                    echo 'N/A';
                                }
                                ?>
                            </span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Location:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($show->location ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <?php if (!empty($show->description)): ?>
                    <hr>
                    <strong>Description:</strong><br>
                    <span class="text-muted"><?php echo nl2br(htmlspecialchars($show->description)); ?></span>
                    <?php endif; ?>
                    <?php if (isset($category) && $category): ?>
                    <hr>
                    <strong>Category:</strong><br>
                    <span class="text-muted"><?php echo htmlspecialchars($category->name ?? 'N/A'); ?></span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Vehicle Information -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-car"></i> Vehicle Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($vehicle) && $vehicle): ?>
                    <h6><?php echo htmlspecialchars(($vehicle->year ?? '') . ' ' . ($vehicle->make ?? '') . ' ' . ($vehicle->model ?? '')); ?></h6>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Color:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($vehicle->color ?? 'N/A'); ?></span>
                        </div>
                        <div class="col-sm-6">
                            <strong>License Plate:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($vehicle->license_plate ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <?php if (!empty($vehicle->vin)): ?>
                    <hr>
                    <strong>VIN:</strong><br>
                    <span class="text-muted"><?php echo htmlspecialchars($vehicle->vin); ?></span>
                    <?php endif; ?>
                    <?php else: ?>
                    <p class="text-muted">Vehicle information not available.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Owner Information -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user"></i> Owner Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (isset($user) && $user): ?>
                    <h6><?php echo htmlspecialchars($user->name ?? 'N/A'); ?></h6>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Email:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($user->email ?? 'N/A'); ?></span>
                        </div>
                        <div class="col-sm-6">
                            <strong>Phone:</strong><br>
                            <span class="text-muted"><?php echo htmlspecialchars($user->phone ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <?php else: ?>
                    <p class="text-muted">Owner information not available.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Vehicle Images -->
    <?php if (!empty($vehicle_images)): ?>
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images"></i> Vehicle Images
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($vehicle_images as $image): ?>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <div class="card">
                                <?php
                                $imagePath = '/' . $image->file_path;
                                ?>
                                <img src="<?php echo $imagePath; ?>" 
                                     class="card-img-top" 
                                     alt="Vehicle Image"
                                     style="height: 200px; object-fit: cover;">
                                <?php if (isset($image->is_primary) && $image->is_primary): ?>
                                <div class="card-body p-2">
                                    <small class="badge badge-primary">Primary Image</small>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Judging Results -->
    <?php if (!empty($judging_results) || !empty($awards)): ?>
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy"></i> Judging Results
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($judging_results)): ?>
                    <h6>Scores</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Category</th>
                                    <th>Score</th>
                                    <th>Judge</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($judging_results as $result): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($result->category_name ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($result->score ?? 'N/A'); ?></td>
                                    <td><?php echo htmlspecialchars($result->judge_name ?? 'N/A'); ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($awards)): ?>
                    <h6 class="mt-3">Awards</h6>
                    <div class="row">
                        <?php foreach ($awards as $award): ?>
                        <div class="col-md-4 mb-2">
                            <div class="alert alert-success">
                                <i class="fas fa-award"></i> <?php echo htmlspecialchars($award->name ?? 'Award'); ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- QR Code -->
    <?php if (!empty($registration->qr_code)): ?>
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-qrcode"></i> QR Code
                    </h5>
                </div>
                <div class="card-body text-center">
                    <img src="/uploads/qrcodes/<?php echo htmlspecialchars($registration->qr_code); ?>" 
                         alt="Registration QR Code" 
                         class="img-fluid" 
                         style="max-width: 300px;">
                    <p class="text-muted mt-2">Scan this QR code for quick access to registration details.</p>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>